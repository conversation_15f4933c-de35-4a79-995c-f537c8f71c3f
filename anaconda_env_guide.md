# Anaconda虚拟环境使用指南

## 🎯 快速开始

### 1. 查看现有环境
```bash
conda env list
```

### 2. 激活环境
```bash
conda activate your_env_name
```

### 3. 验证激活
命令行前面应该显示 `(your_env_name)`

### 4. 运行程序
```bash
python your_script.py
```

### 5. 退出环境
```bash
conda deactivate
```

## 📋 详细步骤

### 步骤1：打开Anaconda Prompt

**方法A：从开始菜单**
1. 点击Windows开始菜单
2. 搜索"Anaconda Prompt"
3. 点击打开

**方法B：从Anaconda Navigator**
1. 打开Anaconda Navigator
2. 点击"Environments"
3. 选择环境，点击绿色播放按钮
4. 选择"Open Terminal"

### 步骤2：查看和选择环境

```bash
# 查看所有环境
conda env list

# 输出示例：
# conda environments:
#
# base                  *  C:\Users\<USER>\anaconda3
# segmentation_env         C:\Users\<USER>\anaconda3\envs\segmentation_env
# myenv                    C:\Users\<USER>\anaconda3\envs\myenv
```

### 步骤3：激活目标环境

```bash
# 激活您的环境（替换为实际环境名）
conda activate segmentation_env
```

**成功激活的标志：**
- 命令行前面出现 `(segmentation_env)`
- 表示当前在该环境中

### 步骤4：安装必要的包

```bash
# 确保在正确的环境中
conda activate segmentation_env

# 安装兼容版本的NumPy
pip install numpy==1.24.3

# 安装MXNet和相关包
pip install mxnet
pip install gluoncv

# 安装其他依赖
pip install pandas pillow opencv-python matplotlib
```

### 步骤5：验证安装

```bash
# 测试导入
python -c "import numpy; print('NumPy版本:', numpy.__version__)"
python -c "import mxnet; print('MXNet版本:', mxnet.__version__)"
python -c "import gluoncv; print('GluonCV版本:', gluoncv.__version__)"
```

### 步骤6：运行您的程序

```bash
# 切换到项目目录
cd "D:\桌面\颜色相关度\整合"

# 运行语义分割脚本
python "Semantic segmentation\StreetscapeSeg\Segment_psnet_ade.py"
```

## 🔧 常见问题解决

### Q1: 激活环境失败
```bash
# 如果conda activate不工作，尝试：
activate segmentation_env

# 或者使用完整路径：
C:\Users\<USER>\anaconda3\Scripts\activate segmentation_env
```

### Q2: 找不到环境
```bash
# 重新创建环境
conda create -n segmentation_env python=3.9

# 激活并安装包
conda activate segmentation_env
pip install numpy==1.24.3 mxnet gluoncv
```

### Q3: 包安装失败
```bash
# 更新conda
conda update conda

# 清理缓存
conda clean --all

# 重新安装
pip install --no-cache-dir numpy==1.24.3
```

## 📱 使用Anaconda Navigator（图形界面）

### 方法1：通过Navigator激活环境

1. **打开Anaconda Navigator**
2. **点击左侧"Environments"**
3. **选择您的环境**
4. **点击绿色播放按钮 ▶️**
5. **选择"Open Terminal"**

### 方法2：在Navigator中管理包

1. **选择环境**
2. **在右侧搜索包名**
3. **点击"Install"安装**

## 🎯 针对您的语义分割项目

### 完整的环境设置流程

```bash
# 1. 打开Anaconda Prompt

# 2. 创建专门的环境（如果还没有）
conda create -n streetview_seg python=3.9

# 3. 激活环境
conda activate streetview_seg

# 4. 安装兼容的NumPy
pip install numpy==1.24.3

# 5. 安装深度学习包
pip install mxnet
pip install gluoncv

# 6. 安装其他依赖
pip install pandas pillow opencv-python matplotlib

# 7. 验证安装
python -c "import mxnet as mx; print('MXNet可以正常导入')"

# 8. 切换到项目目录
cd "D:\桌面\颜色相关度\整合"

# 9. 运行语义分割
python "Semantic segmentation\StreetscapeSeg\Segment_psnet_ade.py"
```

### 每次使用的流程

```bash
# 1. 打开Anaconda Prompt

# 2. 激活环境
conda activate streetview_seg

# 3. 切换到项目目录
cd "D:\桌面\颜色相关度\整合"

# 4. 运行程序
python your_script.py

# 5. 完成后退出环境（可选）
conda deactivate
```

## 🚀 快速命令参考

```bash
# 环境管理
conda env list                    # 查看所有环境
conda activate env_name           # 激活环境
conda deactivate                  # 退出当前环境
conda create -n env_name python=3.9  # 创建新环境
conda remove -n env_name --all    # 删除环境

# 包管理
pip list                          # 查看已安装的包
pip install package_name          # 安装包
pip uninstall package_name        # 卸载包
pip install package_name==version # 安装特定版本

# 验证
python -c "import package_name"   # 测试包导入
python --version                  # 查看Python版本
```

## 💡 使用建议

1. **始终在激活环境后再运行程序**
2. **每个项目使用独立的环境**
3. **定期备份环境配置**
4. **使用requirements.txt记录依赖**

### 导出环境配置
```bash
# 激活环境
conda activate streetview_seg

# 导出环境
conda env export > environment.yml

# 或导出pip包列表
pip freeze > requirements.txt
```

### 从配置恢复环境
```bash
# 从yml文件创建环境
conda env create -f environment.yml

# 从requirements.txt安装包
pip install -r requirements.txt
```

现在您应该可以成功使用Anaconda虚拟环境了！
