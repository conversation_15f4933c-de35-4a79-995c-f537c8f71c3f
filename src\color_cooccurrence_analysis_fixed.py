#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复的颜色共现分析模块
严格按照要求：通过一层聚类的12个颜色在目标文件夹所有图片进行颜色共现矩阵计算
如果任意三个颜色组成的颜色组在同一张图片共现则记录为1
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
import networkx as nx
from collections import defaultdict
import itertools
from PIL import Image
import colorsys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
from src.utils import ensure_dir, get_image_files, get_colors_from_image

class FixedColorCooccurrenceAnalysis:
    """修复的颜色共现分析"""
    
    def __init__(self, config):
        self.config = config
        self.primary_dir = config['data']['primary_dir']
        self.output_dir = config['output']['correlation_dir']
        self.similarity_threshold = config['correlation']['similarity_threshold']
        self.filter_threshold = config['clustering']['filter_threshold']
        ensure_dir(self.output_dir)
        
        # 用于存储分析结果
        self.triplet_cooccurrence = defaultdict(int)
        self.image_color_mapping = {}
        self.analysis_results = {}
    
    def calculate_color_similarity_hsv(self, color1, color2):
        """在HSV空间计算颜色相似度"""
        def rgb_to_hsv_normalized(rgb):
            r, g, b = rgb / 255.0
            return colorsys.rgb_to_hsv(r, g, b)
        
        hsv1 = rgb_to_hsv_normalized(color1)
        hsv2 = rgb_to_hsv_normalized(color2)
        
        # 色调差异（考虑循环特性）
        h_diff = min(abs(hsv1[0] - hsv2[0]), 1 - abs(hsv1[0] - hsv2[0]))
        
        # 饱和度和明度差异
        s_diff = abs(hsv1[1] - hsv2[1])
        v_diff = abs(hsv1[2] - hsv2[2])
        
        # 综合相似度（权重可调整）
        similarity = 1 - (0.6 * h_diff + 0.2 * s_diff + 0.2 * v_diff)
        
        return similarity
    
    def find_matching_colors_in_image(self, image_path, cluster_colors):
        """在图像中找到与聚类颜色匹配的颜色"""
        try:
            # 从图像中提取颜色
            image_pixels = get_colors_from_image(image_path, self.filter_threshold)
            
            if image_pixels is None or len(image_pixels) == 0:
                print(f"⚠️  图像 {os.path.basename(image_path)} 中没有有效颜色")
                return []
            
            # 为每个聚类颜色找到最佳匹配
            matched_cluster_indices = set()
            
            # 使用更严格的匹配策略
            for pixel in image_pixels:
                best_match_idx = -1
                best_similarity = 0
                
                for i, cluster_color in enumerate(cluster_colors):
                    similarity = self.calculate_color_similarity_hsv(pixel, cluster_color)
                    
                    if similarity > best_similarity and similarity >= self.similarity_threshold:
                        best_similarity = similarity
                        best_match_idx = i
                
                if best_match_idx >= 0:
                    matched_cluster_indices.add(best_match_idx)
            
            matched_indices = list(matched_cluster_indices)
            
            print(f"📷 图像 {os.path.basename(image_path)}: 匹配到 {len(matched_indices)} 个聚类颜色 {matched_indices}")
            
            return matched_indices
            
        except Exception as e:
            print(f"❌ 处理图像 {image_path} 时出错: {e}")
            return []
    
    def calculate_triplet_cooccurrence_matrix(self, cluster_colors):
        """计算三色组合的共现矩阵"""
        print("🔄 开始计算三色组合共现矩阵...")
        print(f"   聚类颜色数量: {len(cluster_colors)}")
        print(f"   目标目录: {self.primary_dir}")
        
        # 获取图像文件
        image_files = get_image_files(self.primary_dir)
        
        if not image_files:
            print(f"❌ 在目录 {self.primary_dir} 中没有找到图像文件")
            return None
        
        print(f"   找到 {len(image_files)} 个图像文件")
        
        # 重置共现计数器
        self.triplet_cooccurrence.clear()
        self.image_color_mapping.clear()
        
        # 生成所有可能的三色组合
        all_triplets = list(itertools.combinations(range(len(cluster_colors)), 3))
        print(f"   总共有 {len(all_triplets)} 个可能的三色组合")
        
        # 初始化所有三色组合的计数为0
        for triplet in all_triplets:
            self.triplet_cooccurrence[triplet] = 0
        
        # 处理每个图像
        valid_images = 0
        
        for img_path in image_files:
            # 找到图像中匹配的聚类颜色
            matched_indices = self.find_matching_colors_in_image(img_path, cluster_colors)
            
            # 记录图像-颜色映射
            self.image_color_mapping[os.path.basename(img_path)] = matched_indices
            
            if len(matched_indices) >= 3:
                valid_images += 1
                
                # 计算该图像中所有可能的三色组合
                image_triplets = list(itertools.combinations(matched_indices, 3))
                
                # 为每个在该图像中共现的三色组合计数+1
                for triplet in image_triplets:
                    # 确保三元组按升序排列（标准化）
                    sorted_triplet = tuple(sorted(triplet))
                    self.triplet_cooccurrence[sorted_triplet] += 1
                
                print(f"   图像 {os.path.basename(img_path)}: {len(image_triplets)} 个三色组合")
            else:
                print(f"   图像 {os.path.basename(img_path)}: 颜色不足3个，跳过")
        
        print(f"✅ 共现矩阵计算完成")
        print(f"   有效图像数量: {valid_images}/{len(image_files)}")
        
        # 统计结果
        non_zero_triplets = {k: v for k, v in self.triplet_cooccurrence.items() if v > 0}
        print(f"   有共现记录的三色组合: {len(non_zero_triplets)}/{len(all_triplets)}")
        
        if non_zero_triplets:
            max_count = max(non_zero_triplets.values())
            print(f"   最高共现次数: {max_count}")
        
        return self.triplet_cooccurrence
    
    def get_top_triplet_colors(self, cluster_colors, top_n=1):
        """获取共现次数最高的三色组合"""
        if not self.triplet_cooccurrence:
            print("❌ 没有共现数据")
            return None, None, None
        
        # 按共现次数排序
        sorted_triplets = sorted(self.triplet_cooccurrence.items(), 
                               key=lambda x: x[1], reverse=True)
        
        print(f"\n🏆 前{min(10, len(sorted_triplets))}个三色组合共现次数:")
        for i, (triplet, count) in enumerate(sorted_triplets[:10]):
            color1_idx, color2_idx, color3_idx = triplet
            print(f"   {i+1}. 颜色组合 [{color1_idx}, {color2_idx}, {color3_idx}]: {count} 次")
            print(f"      RGB{tuple(map(int, cluster_colors[color1_idx]))}, "
                  f"RGB{tuple(map(int, cluster_colors[color2_idx]))}, "
                  f"RGB{tuple(map(int, cluster_colors[color3_idx]))}")
        
        if not sorted_triplets or sorted_triplets[0][1] == 0:
            print("❌ 没有找到有效的三色组合")
            return None, None, None
        
        # 获取前top_n个三色组合
        top_triplets = sorted_triplets[:top_n]
        
        # 返回第一个（最高共现次数的）三色组合
        best_triplet_indices, best_count = top_triplets[0]
        best_triplet_colors = [cluster_colors[i] for i in best_triplet_indices]
        
        print(f"\n✅ 选择的最佳三色组合:")
        print(f"   索引: {best_triplet_indices}")
        print(f"   共现次数: {best_count}")
        print(f"   颜色: {[tuple(map(int, color)) for color in best_triplet_colors]}")
        
        return list(best_triplet_indices), best_triplet_colors, [best_count] * 3
    
    def save_analysis_results(self, cluster_colors):
        """保存分析结果到文件"""
        # 保存详细的共现分析结果
        results_file = os.path.join(self.output_dir, "triplet_cooccurrence_analysis.txt")
        
        with open(results_file, 'w', encoding='utf-8') as f:
            f.write("颜色三元组共现分析结果\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"分析目录: {self.primary_dir}\n")
            f.write(f"聚类颜色数量: {len(cluster_colors)}\n")
            f.write(f"图像文件数量: {len(self.image_color_mapping)}\n")
            f.write(f"相似度阈值: {self.similarity_threshold}\n\n")
            
            # 聚类颜色信息
            f.write("聚类颜色列表:\n")
            f.write("-" * 40 + "\n")
            for i, color in enumerate(cluster_colors):
                f.write(f"C{i:2d}: RGB{tuple(map(int, color))}\n")
            f.write("\n")
            
            # 图像-颜色映射
            f.write("图像-颜色映射:\n")
            f.write("-" * 40 + "\n")
            for img_name, color_indices in self.image_color_mapping.items():
                f.write(f"{img_name}: {color_indices}\n")
            f.write("\n")
            
            # 三色组合共现统计
            f.write("三色组合共现统计:\n")
            f.write("-" * 40 + "\n")
            
            sorted_triplets = sorted(self.triplet_cooccurrence.items(), 
                                   key=lambda x: x[1], reverse=True)
            
            for triplet, count in sorted_triplets:
                if count > 0:  # 只显示有共现的组合
                    color1_idx, color2_idx, color3_idx = triplet
                    f.write(f"组合 [{color1_idx:2d}, {color2_idx:2d}, {color3_idx:2d}]: {count:3d} 次 - ")
                    f.write(f"RGB{tuple(map(int, cluster_colors[color1_idx]))}, ")
                    f.write(f"RGB{tuple(map(int, cluster_colors[color2_idx]))}, ")
                    f.write(f"RGB{tuple(map(int, cluster_colors[color3_idx]))}\n")
        
        print(f"✅ 分析结果已保存到: {results_file}")
        
        # 保存CSV格式的共现矩阵
        csv_file = os.path.join(self.output_dir, "triplet_cooccurrence_matrix.csv")
        
        import pandas as pd
        
        # 创建DataFrame
        triplet_data = []
        for triplet, count in sorted_triplets:
            color1_idx, color2_idx, color3_idx = triplet
            triplet_data.append({
                'Color1_Index': color1_idx,
                'Color2_Index': color2_idx, 
                'Color3_Index': color3_idx,
                'Color1_RGB': tuple(map(int, cluster_colors[color1_idx])),
                'Color2_RGB': tuple(map(int, cluster_colors[color2_idx])),
                'Color3_RGB': tuple(map(int, cluster_colors[color3_idx])),
                'Cooccurrence_Count': count
            })
        
        df = pd.DataFrame(triplet_data)
        df.to_csv(csv_file, index=False, encoding='utf-8')
        
        print(f"✅ CSV数据已保存到: {csv_file}")
    
    def process(self, cluster_colors):
        """主处理函数"""
        print("🚀 开始颜色共现分析...")
        
        # 计算三色组合共现矩阵
        cooccurrence_matrix = self.calculate_triplet_cooccurrence_matrix(cluster_colors)
        
        if cooccurrence_matrix is None:
            print("❌ 共现矩阵计算失败")
            return None, None, None
        
        # 获取最佳三色组合
        top_indices, top_colors, top_correlations = self.get_top_triplet_colors(cluster_colors)
        
        if top_indices is None:
            print("❌ 没有找到有效的三色组合")
            return None, None, None
        
        # 保存分析结果
        self.save_analysis_results(cluster_colors)
        
        print("✅ 颜色共现分析完成")
        
        return top_indices, top_colors, top_correlations
