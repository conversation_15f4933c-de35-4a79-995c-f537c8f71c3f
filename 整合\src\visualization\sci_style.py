#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SCI期刊风格配置模块
提供专业的学术期刊图表风格
"""

import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
import numpy as np
from matplotlib.colors import LinearSegmentedColormap
import seaborn as sns

class SCIStyle:
    """SCI期刊风格配置类"""
    
    # 专业配色方案
    COLORS = {
        'primary': '#2E86AB',      # 深蓝色 - 主要数据
        'secondary': "#8C3161",    # 深紫红 - 次要数据
        'accent1': '#F18F01',      # 橙色 - 强调色1
        'accent2': '#C73E1D',      # 红色 - 强调色2
        'neutral1': '#5D737E',     # 灰蓝色 - 中性色1
        'neutral2': '#64A6BD',     # 浅蓝色 - 中性色2
        'success': '#2D5016',      # 深绿色 - 成功/正向
        'warning': '#8B4513',      # 棕色 - 警告
        'background': '#FAFAFA',   # 背景色
        'grid': '#E0E0E0',         # 网格色
        'text': '#2E2E2E'          # 文本色
    }
    
    # 专业配色序列
    COLOR_SEQUENCES = {
        'qualitative': ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#5D737E', '#64A6BD'],
        'sequential_blue': ['#F7FBFF', '#DEEBF7', '#C6DBEF', '#9ECAE1', '#6BAED6', '#4292C6', '#2171B5', '#08519C', '#08306B'],
        'sequential_red': ['#FFF5F0', '#FEE0D2', '#FCBBA1', '#FC9272', '#FB6A4A', '#EF3B2C', '#CB181D', '#A50F15', '#67000D'],
        'diverging': ['#67001F', '#B2182B', '#D6604D', '#F4A582', '#FDDBC7', '#F7F7F7', '#D1E5F0', '#92C5DE', '#4393C3', '#2166AC', '#053061']
    }
    
    @classmethod
    def setup_style(cls):
        """设置SCI期刊风格"""
        # 使用专业风格
        plt.style.use('default')  # 重置为默认风格
        
        # 设置字体和基本参数
        plt.rcParams.update({
            # 字体设置
            'font.family': ['Times New Roman', 'DejaVu Serif', 'serif'],
            'font.size': 10,
            'axes.titlesize': 12,
            'axes.labelsize': 11,
            'xtick.labelsize': 9,
            'ytick.labelsize': 9,
            'legend.fontsize': 9,
            'figure.titlesize': 14,
            
            # 线条和边框
            'axes.linewidth': 1.0,
            'grid.linewidth': 0.5,
            'lines.linewidth': 1.5,
            'patch.linewidth': 0.8,
            'xtick.major.width': 1.0,
            'ytick.major.width': 1.0,
            'xtick.minor.width': 0.6,
            'ytick.minor.width': 0.6,
            
            # 颜色设置
            'axes.edgecolor': cls.COLORS['text'],
            'axes.facecolor': 'white',
            'figure.facecolor': 'white',
            'text.color': cls.COLORS['text'],
            
            # 网格设置
            'axes.grid': True,
            'grid.alpha': 0.3,
            'grid.color': cls.COLORS['grid'],
            'axes.axisbelow': True,
            
            # 保存设置
            'savefig.dpi': 300,
            'savefig.bbox': 'tight',
            'savefig.facecolor': 'white',
            'savefig.edgecolor': 'none',
            'savefig.pad_inches': 0.1,
            
            # 其他设置
            'axes.unicode_minus': False,
            'axes.spines.top': False,
            'axes.spines.right': False,
            'xtick.direction': 'out',
            'ytick.direction': 'out'
        })
    
    @classmethod
    def get_color_palette(cls, n_colors=6, palette_type='qualitative'):
        """获取专业配色方案"""
        if palette_type == 'qualitative':
            colors = cls.COLOR_SEQUENCES['qualitative']
            if n_colors <= len(colors):
                return colors[:n_colors]
            else:
                # 如果需要更多颜色，使用插值
                return sns.color_palette("husl", n_colors)
        elif palette_type == 'sequential':
            return sns.color_palette("Blues_r", n_colors)
        elif palette_type == 'diverging':
            return sns.color_palette("RdBu_r", n_colors)
        else:
            return cls.COLOR_SEQUENCES['qualitative'][:n_colors]
    
    @classmethod
    def create_custom_colormap(cls, colors, name='custom'):
        """创建自定义颜色映射"""
        return LinearSegmentedColormap.from_list(name, colors)
    
    @classmethod
    def format_axes(cls, ax, title=None, xlabel=None, ylabel=None, 
                   spine_style='minimal', grid_style='major'):
        """格式化坐标轴"""
        # 设置标题和标签
        if title:
            ax.set_title(title, fontweight='bold', pad=15)
        if xlabel:
            ax.set_xlabel(xlabel, fontweight='normal')
        if ylabel:
            ax.set_ylabel(ylabel, fontweight='normal')
        
        # 设置边框样式
        if spine_style == 'minimal':
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.spines['left'].set_linewidth(1.0)
            ax.spines['bottom'].set_linewidth(1.0)
        elif spine_style == 'box':
            for spine in ax.spines.values():
                spine.set_linewidth(1.0)
        
        # 设置网格样式
        if grid_style == 'major':
            ax.grid(True, linestyle='-', alpha=0.3)
        elif grid_style == 'minor':
            ax.grid(True, linestyle=':', alpha=0.2)
            ax.minorticks_on()
        elif grid_style == 'both':
            ax.grid(True, which='major', linestyle='-', alpha=0.3)
            ax.grid(True, which='minor', linestyle=':', alpha=0.2)
            ax.minorticks_on()
        
        # 设置刻度
        ax.tick_params(axis='both', which='major', labelsize=9)
        ax.tick_params(axis='both', which='minor', labelsize=8)
    
    @classmethod
    def add_statistical_annotations(cls, ax, data, positions, test_type='t-test'):
        """添加统计显著性标注"""
        # 这里可以添加统计检验的标注
        # 例如 p-value, 置信区间等
        pass
    
    @classmethod
    def create_legend(cls, ax, labels, colors=None, style='outside'):
        """创建专业图例"""
        if colors is None:
            colors = cls.get_color_palette(len(labels))
        
        # 创建图例元素
        legend_elements = [mpatches.Patch(color=color, label=label) 
                          for color, label in zip(colors, labels)]
        
        if style == 'outside':
            legend = ax.legend(handles=legend_elements, 
                             bbox_to_anchor=(1.05, 1), 
                             loc='upper left',
                             frameon=True,
                             fancybox=False,
                             shadow=False,
                             framealpha=0.9,
                             edgecolor='gray')
        else:
            legend = ax.legend(handles=legend_elements,
                             frameon=True,
                             fancybox=False,
                             shadow=False,
                             framealpha=0.9,
                             edgecolor='gray')
        
        # 设置图例样式
        legend.get_frame().set_linewidth(0.8)
        return legend
    
    @classmethod
    def save_figure(cls, fig, filepath, format='png', dpi=300):
        """保存高质量图片"""
        fig.savefig(filepath, 
                   format=format,
                   dpi=dpi,
                   bbox_inches='tight',
                   facecolor='white',
                   edgecolor='none',
                   pad_inches=0.1)
    
    @classmethod
    def create_subplot_labels(cls, fig, axes, labels=None, style='uppercase'):
        """为子图添加标签 (A, B, C, D...)"""
        if labels is None:
            if style == 'uppercase':
                labels = [chr(65 + i) for i in range(len(axes.flat))]
            else:
                labels = [chr(97 + i) for i in range(len(axes.flat))]
        
        for ax, label in zip(axes.flat, labels):
            ax.text(-0.1, 1.05, f'({label})', transform=ax.transAxes,
                   fontsize=12, fontweight='bold', va='bottom', ha='right')
    
    @classmethod
    def add_significance_bar(cls, ax, x1, x2, y, p_value, height=0.05):
        """添加显著性检验横线"""
        # 绘制横线
        ax.plot([x1, x2], [y, y], 'k-', linewidth=1)
        ax.plot([x1, x1], [y, y-height], 'k-', linewidth=1)
        ax.plot([x2, x2], [y, y-height], 'k-', linewidth=1)
        
        # 添加p值标注
        if p_value < 0.001:
            sig_text = '***'
        elif p_value < 0.01:
            sig_text = '**'
        elif p_value < 0.05:
            sig_text = '*'
        else:
            sig_text = 'ns'
        
        ax.text((x1 + x2) / 2, y + height/2, sig_text, 
               ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    @classmethod
    def format_scientific_notation(cls, ax, axis='y', threshold=1000):
        """格式化科学计数法"""
        if axis == 'y':
            ax.ticklabel_format(style='scientific', axis='y', scilimits=(0,0))
            ax.yaxis.get_offset_text().set_fontsize(8)
        elif axis == 'x':
            ax.ticklabel_format(style='scientific', axis='x', scilimits=(0,0))
            ax.xaxis.get_offset_text().set_fontsize(8)
    
    @classmethod
    def add_inset_axes(cls, ax, position, width=0.3, height=0.3):
        """添加插图坐标轴"""
        from mpl_toolkits.axes_grid1.inset_locator import inset_axes
        inset_ax = inset_axes(ax, width=f"{width*100}%", height=f"{height*100}%", 
                             loc=position, borderpad=2)
        cls.format_axes(inset_ax, spine_style='box', grid_style='major')
        return inset_ax
