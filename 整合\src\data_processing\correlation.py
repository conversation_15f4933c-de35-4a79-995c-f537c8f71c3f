import os
import numpy as np
import pandas as pd
import sys
from collections import defaultdict
import matplotlib.pyplot as plt
import networkx as nx
from PIL import Image
import itertools

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from src.utils import rgb_to_hsv, ensure_dir, visualize_color_network, get_colors_from_image

class ColorCorrelation:
    """颜色相关度计算类"""
    
    def __init__(self, config):
        self.config = config
        self.similarity_threshold = config['correlation']['similarity_threshold']
        self.top_colors_count = config['correlation']['top_colors']
        self.output_dir = config['output']['correlation_dir']
        self.primary_dir = config['data']['primary_dir']
        self.filter_threshold = config['clustering']['filter_threshold']
        ensure_dir(self.output_dir)
    
    def calculate_color_similarity(self, color1, color2):
        """计算HSV空间中的颜色相似度"""
        hsv1 = rgb_to_hsv(color1)
        hsv2 = rgb_to_hsv(color2)
        
        # 色调差异（考虑循环特性）
        h_diff = min(abs(hsv1[0] - hsv2[0]), 1 - abs(hsv1[0] - hsv2[0]))
        
        # 饱和度和明度差异
        s_diff = abs(hsv1[1] - hsv2[1])
        v_diff = abs(hsv1[2] - hsv2[2])
        
        # 综合相似度（权重可根据需要调整）
        similarity = 1 - (0.6 * h_diff + 0.2 * s_diff + 0.2 * v_diff)
        
        return similarity
    
    def is_color_match(self, color1, color2):
        """判断两个颜色是否匹配，基于HSV空间的相似度"""
        similarity = self.calculate_color_similarity(color1, color2)
        return similarity >= self.similarity_threshold
    
    def get_dominant_colors(self, image_path, cluster_centers):
        """获取图像中的主要颜色（与聚类中心最匹配的颜色）"""
        try:
            # 从图像中提取颜色
            pixels = get_colors_from_image(image_path, self.filter_threshold)
            if pixels is None or len(pixels) == 0:
                print(f"警告: 图像 {image_path} 中没有有效的颜色")
                # 如果没有有效颜色，强制返回所有聚类中心索引
                return list(range(len(cluster_centers)))

            # 为每个像素找到最匹配的聚类中心
            dominant_colors = set()
            color_counts = {}  # 记录每个聚类中心的匹配次数

            for pixel in pixels:
                best_match = -1
                best_similarity = -1

                for i, center in enumerate(cluster_centers):
                    similarity = self.calculate_color_similarity(pixel, center)
                    if similarity > best_similarity:
                        best_similarity = similarity
                        best_match = i

                if best_match >= 0:
                    dominant_colors.add(best_match)
                    color_counts[best_match] = color_counts.get(best_match, 0) + 1

            # 如果主要颜色少于3个，强制包含更多颜色
            if len(dominant_colors) < 3:
                # 按匹配次数排序，选择前12个（或所有可用的）
                sorted_colors = sorted(color_counts.items(), key=lambda x: x[1], reverse=True)

                # 添加更多颜色直到达到12个或用完所有聚类中心
                for i in range(len(cluster_centers)):
                    if i not in dominant_colors:
                        dominant_colors.add(i)
                    if len(dominant_colors) >= 12:
                        break

            return list(dominant_colors)
        except Exception as e:
            print(f"处理图像 {image_path} 时出错: {str(e)}")
            # 出错时返回所有聚类中心索引
            return list(range(len(cluster_centers)))
    
    def calculate_adjacency_matrix(self, colors):
        """计算邻接矩阵 - 基于图像中颜色的共现次数
        
        参数:
            colors: 聚类中心颜色列表
            
        返回:
            邻接矩阵
        """
        num_colors = len(colors)
        adjacency_matrix = np.zeros((num_colors, num_colors))
        
        # 获取图像文件列表
        from src.utils import get_image_files
        image_files = get_image_files(self.primary_dir)
        
        if not image_files:
            print(f"警告: 在目录 {self.primary_dir} 中没有找到图像文件")
            return adjacency_matrix
        
        print(f"找到 {len(image_files)} 个图像文件")
        
        # 三色组合的共现次数
        color_triplet_counts = defaultdict(int)
        
        # 处理每个图像
        for img_path in image_files:
            try:
                # 获取图像中的主要颜色索引
                dominant_color_indices = self.get_dominant_colors(img_path, colors)

                # 强制确保至少有3个颜色用于分析
                if len(dominant_color_indices) < 3:
                    # 补充颜色索引直到至少有3个
                    for i in range(len(colors)):
                        if i not in dominant_color_indices:
                            dominant_color_indices.append(i)
                        if len(dominant_color_indices) >= 3:
                            break
                    print(f"图像 {os.path.basename(img_path)} 强制补充颜色，当前颜色数: {len(dominant_color_indices)}")
                else:
                    print(f"图像 {os.path.basename(img_path)} 中的主要颜色索引: {dominant_color_indices}")

                # 计算三个颜色的组合共现次数
                for triplet in itertools.combinations(dominant_color_indices, 3):
                    color_triplet_counts[triplet] += 1

                    # 同时更新邻接矩阵（两两颜色的共现）
                    for i, j in itertools.combinations(triplet, 2):
                        adjacency_matrix[i][j] += 1
                        adjacency_matrix[j][i] += 1  # 对称矩阵
            except Exception as e:
                print(f"处理图像 {img_path} 时出错: {str(e)}")
        
        # 保存三色组合的共现次数
        triplet_file = os.path.join(self.output_dir, "color_triplets.txt")
        with open(triplet_file, 'w') as f:
            f.write("三色组合共现次数:\n")
            f.write("=" * 40 + "\n\n")
            
            # 按共现次数排序
            sorted_triplets = sorted(color_triplet_counts.items(), key=lambda x: x[1], reverse=True)
            
            for triplet, count in sorted_triplets:
                color1, color2, color3 = triplet
                f.write(f"颜色组合 [{color1}, {color2}, {color3}]: {count} 次\n")
                f.write(f"  RGB{tuple(map(int, colors[color1]))}, RGB{tuple(map(int, colors[color2]))}, RGB{tuple(map(int, colors[color3]))}\n\n")
        
        print(f"三色组合共现次数已保存到: {triplet_file}")
        
        return adjacency_matrix
    
    def normalize_matrix(self, matrix):
        """归一化矩阵，使每行的总和为1"""
        row_sums = matrix.sum(axis=1)
        normalized_matrix = np.zeros_like(matrix, dtype=float)
        
        for i in range(len(matrix)):
            if row_sums[i] > 0:
                normalized_matrix[i] = matrix[i] / row_sums[i]
        
        return normalized_matrix
    
    def calculate_strength_matrix(self, adjacency_matrix):
        """计算强度矩阵（每个颜色的相关强度）"""
        strength = adjacency_matrix.sum(axis=1)
        return strength
    
    def calculate_weight_ratio_matrix(self, adjacency_matrix, strength):
        """计算权重比例矩阵"""
        weight_ratio = np.zeros_like(adjacency_matrix, dtype=float)
        
        for i in range(len(adjacency_matrix)):
            if strength[i] > 0:
                weight_ratio[i] = adjacency_matrix[i] / strength[i]
        
        return weight_ratio
    
    def find_top_related_colors(self, matrix, colors):
        """找出相关度最高的颜色"""
        # 计算每个颜色的总相关度
        color_correlation = matrix.sum(axis=1)
        
        # 获取相关度排序索引
        sorted_indices = np.argsort(color_correlation)[::-1]
        
        # 取前N个颜色
        top_indices = sorted_indices[:self.top_colors_count]
        top_colors = [colors[i] for i in top_indices]
        top_correlations = [color_correlation[i] for i in top_indices]
        
        return top_indices, top_colors, top_correlations
    
    def find_top_color_triplets(self, colors):
        """找出共现次数最高的三色组合"""
        # 读取三色组合文件
        triplet_file = os.path.join(self.output_dir, "color_triplets.txt")
        if not os.path.exists(triplet_file):
            print(f"警告: 三色组合文件不存在: {triplet_file}")
            return None
        
        with open(triplet_file, 'r') as f:
            lines = f.readlines()
        
        # 解析文件内容
        triplets = []
        i = 3  # 跳过前3行
        while i < len(lines):
            if lines[i].startswith("颜色组合"):
                triplet_str = lines[i].split("[")[1].split("]")[0]
                indices = [int(idx.strip()) for idx in triplet_str.split(",")]
                count = int(lines[i].split(":")[1].split("次")[0].strip())
                triplets.append((indices, count))
                i += 3  # 跳过下一个空行
            else:
                i += 1
        
        if not triplets:
            print("警告: 没有找到有效的三色组合")
            return None
        
        # 取共现次数最高的三色组合
        top_triplet = triplets[0][0]
        top_colors = [colors[i] for i in top_triplet]
        
        return top_colors
    
    def process(self, primary_colors, primary_color_groups=None):
        """处理颜色相关度
        
        参数:
            primary_colors: 一层数据的聚类颜色
            primary_color_groups: 不再使用，保留参数以兼容旧代码
            
        返回:
            相关度最高的三个颜色
        """
        print("开始计算颜色相关度...")
        
        # 计算邻接矩阵
        adjacency_matrix = self.calculate_adjacency_matrix(primary_colors)
        
        # 保存邻接矩阵
        pd.DataFrame(adjacency_matrix).to_csv(
            os.path.join(self.output_dir, "adjacency_matrix.csv"), 
            index=False, header=False
        )
        
        # 计算强度矩阵
        strength = self.calculate_strength_matrix(adjacency_matrix)
        pd.DataFrame(strength).to_csv(
            os.path.join(self.output_dir, "strength_matrix.csv"), 
            index=False, header=False
        )
        
        # 计算归一化矩阵
        normalized_matrix = self.normalize_matrix(adjacency_matrix)
        pd.DataFrame(normalized_matrix).to_csv(
            os.path.join(self.output_dir, "normalized_matrix.csv"), 
            index=False, header=False
        )
        
        # 计算权重比例矩阵
        weight_ratio = self.calculate_weight_ratio_matrix(adjacency_matrix, strength)
        pd.DataFrame(weight_ratio).to_csv(
            os.path.join(self.output_dir, "weight_ratio_matrix.csv"), 
            index=False, header=False
        )
        
        # 尝试找出共现次数最高的三色组合
        top_triplet_colors = self.find_top_color_triplets(primary_colors)
        
        if top_triplet_colors and len(top_triplet_colors) == 3:
            print("使用共现次数最高的三色组合")
            top_colors = top_triplet_colors
            # 修复numpy数组比较问题 - 使用更高效的方法
            top_indices = []
            for color in top_colors:
                # 使用广播和all()来比较数组
                matches = np.all(primary_colors == color, axis=1)
                if np.any(matches):
                    top_indices.append(np.where(matches)[0][0])
                else:
                    # 如果没有完全匹配，找最相似的
                    distances = np.linalg.norm(primary_colors - color, axis=1)
                    top_indices.append(np.argmin(distances))
            top_correlations = [strength[i] for i in top_indices]
        else:
            # 如果没有找到有效的三色组合，则使用相关度最高的颜色
            print("使用相关度最高的三个颜色")
            top_indices, top_colors, top_correlations = self.find_top_related_colors(
                adjacency_matrix, primary_colors)
        
        # 保存结果
        with open(os.path.join(self.output_dir, "correlation_results.txt"), 'w') as f:
            f.write("颜色相关度结果\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"相关度最高的 {len(top_colors)} 种颜色：\n")
            
            for i, (color, correlation) in enumerate(zip(top_colors, top_correlations)):
                f.write(f"{i+1}. RGB{tuple(map(int, color))} - 相关度: {correlation:.2f}\n")
        
        # 可视化颜色网络
        network_path = os.path.join(self.output_dir, "color_network_graph.png")
        visualize_color_network(adjacency_matrix, primary_colors, network_path)
        
        print(f"颜色相关度计算完成，结果保存在: {self.output_dir}")
        
        return top_indices, top_colors, top_correlations