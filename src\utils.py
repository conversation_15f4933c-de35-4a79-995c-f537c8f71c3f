import os
import json
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import colorsys
import matplotlib.pyplot as plt
import matplotlib
from matplotlib.font_manager import FontProperties

# 设置中文字体支持
def setup_chinese_font():
    """设置中文字体支持"""
    try:
        # 尝试设置中文字体
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial Unicode MS']
        for font_name in chinese_fonts:
            try:
                font_prop = FontProperties(fname=font_name)
                matplotlib.rcParams['font.sans-serif'] = [font_name]
                matplotlib.rcParams['axes.unicode_minus'] = False
                print(f"成功设置中文字体: {font_name}")
                return
            except:
                continue

        # 如果都失败了，使用默认设置
        matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans']
        matplotlib.rcParams['axes.unicode_minus'] = False
        print("使用默认字体设置")
    except Exception as e:
        print(f"字体设置失败: {str(e)}")

# 初始化字体设置
setup_chinese_font()

def load_config(config_path='./config/settings.json'):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def ensure_dir(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)

def get_image_files(directory):
    """获取目录中的所有图像文件"""
    extensions = ('.jpg', '.jpeg', '.png', '.bmp')
    result = []
    
    try:
        # 处理路径可能包含空格的情况
        for f in os.listdir(directory):
            if f.lower().endswith(extensions):
                file_path = os.path.join(directory, f)
                # 检查文件是否存在
                if os.path.isfile(file_path):
                    result.append(file_path)
                else:
                    print(f"警告: 文件不存在 {file_path}")
    except Exception as e:
        print(f"读取目录 {directory} 时出错: {str(e)}")
    
    return result

# 颜色空间转换函数
def rgb_to_hsv(color):
    """RGB颜色转HSV"""
    r, g, b = color[0]/255.0, color[1]/255.0, color[2]/255.0
    return colorsys.rgb_to_hsv(r, g, b)

def hsv_to_rgb(h, s, v):
    """HSV颜色转RGB"""
    r, g, b = colorsys.hsv_to_rgb(h, s, v)
    return (int(r * 255), int(g * 255), int(b * 255))

def rgb_to_lab(r, g, b):
    """RGB颜色转Lab"""
    # 将RGB转换为XYZ
    r = r / 255.0
    g = g / 255.0
    b = b / 255.0
    
    r = r / 12.92 if r <= 0.04045 else ((r + 0.055)/1.055)**2.4
    g = g / 12.92 if g <= 0.04045 else ((g + 0.055)/1.055)**2.4
    b = b / 12.92 if b <= 0.04045 else ((b + 0.055)/1.055)**2.4
    
    x = r * 0.4124 + g * 0.3576 + b * 0.1805
    y = r * 0.2126 + g * 0.7152 + b * 0.0722
    z = r * 0.0193 + g * 0.1192 + b * 0.9505
    
    # 将XYZ转换为Lab
    x = x / 0.95047
    z = z / 1.08883
    
    x = x ** (1/3) if x > 0.008856 else 7.787 * x + 16/116
    y = y ** (1/3) if y > 0.008856 else 7.787 * y + 16/116
    z = z ** (1/3) if z > 0.008856 else 7.787 * z + 16/116
    
    L = (116 * y) - 16
    a = 500 * (x - y)
    b = 200 * (y - z)
    
    return (L, a, b)

def lab_to_rgb(L, a, b):
    """Lab颜色转RGB"""
    y = (L + 16) / 116
    x = a / 500 + y
    z = y - b / 200
    
    x = 0.95047 * (x**3 if x > 0.206893 else (x - 16/116)/7.787)
    y = 1.00000 * (y**3 if y > 0.206893 else (y - 16/116)/7.787)
    z = 1.08883 * (z**3 if z > 0.206893 else (z - 16/116)/7.787)
    
    r = x *  3.2406 + y * -1.5372 + z * -0.4986
    g = x * -0.9689 + y *  1.8758 + z *  0.0415
    b = x *  0.0557 + y * -0.2040 + z *  1.0570
    
    r = 255 * (1.055 * (r**(1/2.4)) - 0.055 if r > 0.0031308 else 12.92 * r)
    g = 255 * (1.055 * (g**(1/2.4)) - 0.055 if g > 0.0031308 else 12.92 * g)
    b = 255 * (1.055 * (b**(1/2.4)) - 0.055 if b > 0.0031308 else 12.92 * b)
    
    return (np.clip(r,0,255), np.clip(g,0,255), np.clip(b,0,255))

# 图像处理函数
def get_colors_from_image(img_path, filter_threshold=30):
    """从图片中提取颜色（过滤黑色）"""
    img = Image.open(img_path)
    if img.mode != 'RGB':
        img = img.convert('RGB')
    img_array = np.array(img)
    pixels = img_array.reshape(-1, 3)
    
    # 过滤黑色像素
    mask = np.mean(pixels, axis=1) >= filter_threshold
    filtered_pixels = pixels[mask]
    
    if len(filtered_pixels) == 0:
        return None
    return filtered_pixels

def create_color_block(color, output_path, width=800, height=100):
    """创建颜色块图像"""
    img = Image.new('RGB', (width, height))
    draw = ImageDraw.Draw(img)
    draw.rectangle([(0, 0), (width, height)], fill=tuple(map(int, color)))
    img.save(output_path)

def create_color_palette(colors, proportions=None, output_path=None, title="颜色比例色板", 
                         width=800, height=200):
    """创建颜色比例色板图"""
    if proportions is None:
        proportions = [1/len(colors)] * len(colors)
    
    # 创建新图像
    palette_img = Image.new('RGB', (width, height), (255, 255, 255))
    draw = ImageDraw.Draw(palette_img)
    
    # 尝试加载字体
    try:
        font = ImageFont.truetype("simhei.ttf", 16)  # 尝试加载中文字体
    except IOError:
        try:
            font = ImageFont.truetype("arial.ttf", 16)
        except IOError:
            font = ImageFont.load_default()
    
    # 绘制标题
    draw.text((width//2 - 100, 10), title, fill=(0, 0, 0), font=font)
    
    # 计算色块高度和起始位置
    color_height = height - 50
    color_y = 40
    
    # 绘制色块
    x_pos = 0
    for i, (color, prop) in enumerate(zip(colors, proportions)):
        # 计算色块宽度
        block_width = int(width * prop)
        
        # 绘制色块
        color_tuple = tuple(map(int, color))
        draw.rectangle([(x_pos, color_y), (x_pos + block_width, color_y + color_height)], 
                      fill=color_tuple)
        
        # 为了更好的可读性，选择对比色作为文本颜色
        text_color = (0, 0, 0) if sum(color) > 384 else (255, 255, 255)
        
        # 绘制颜色信息
        color_text = "RGB{}".format(tuple(map(int, color)))
        draw.text((x_pos + 5, color_y + 10), color_text, fill=text_color, font=font)
        
        # 绘制比例信息
        prop_text = "{:.1f}%".format(prop * 100)
        draw.text((x_pos + 5, color_y + color_height - 30), prop_text, fill=text_color, font=font)
        
        # 更新x位置
        x_pos += block_width
    
    # 保存或返回图像
    if output_path:
        palette_img.save(output_path)
        return output_path
    else:
        return palette_img

def visualize_color_network(adjacency_matrix, colors, output_path=None):
    """可视化颜色网络关系"""
    plt.figure(figsize=(12, 10))
    
    # 创建一个有向图
    G = nx.DiGraph()
    
    # 添加节点和边
    for i in range(len(colors)):
        G.add_node(i, color=colors[i])
    
    # 找出边的最大权重，用于归一化
    max_weight = np.max(adjacency_matrix) if np.max(adjacency_matrix) > 0 else 1
    
    # 添加边（只添加非零权重的边）
    for i in range(len(colors)):
        for j in range(len(colors)):
            if adjacency_matrix[i][j] > 0:
                # 归一化权重，使其在0.5到5之间
                normalized_weight = 0.5 + (adjacency_matrix[i][j] / max_weight) * 4.5
                G.add_edge(i, j, weight=normalized_weight)
    
    # 设置节点颜色
    node_colors = []
    for i in range(len(colors)):
        r, g, b = map(int, colors[i])
        # 将RGB值转换为0-1范围的浮点数
        node_colors.append((r/255.0, g/255.0, b/255.0))
    
    # 设置边的宽度根据权重 - 增强对比度
    weights = [G[u][v]['weight'] for u, v in G.edges()]
    if weights:
        max_weight = max(weights)
        min_weight = min(weights)
        # 使用非线性映射增强对比度
        edge_widths = []
        for weight in weights:
            if max_weight > min_weight:
                # 归一化到0-1，然后应用平方函数增强对比
                normalized = (weight - min_weight) / (max_weight - min_weight)
                enhanced = normalized ** 0.5  # 平方根函数，让强相关更突出
                width = 1 + enhanced * 8  # 线条宽度范围1-9
            else:
                width = 3  # 如果权重都相同，使用中等宽度
            edge_widths.append(width)
    else:
        edge_widths = [3]
    
    # 设置节点大小
    node_sizes = [600] * len(colors)
    
    # 使用spring_layout生成更好的布局
    pos = nx.spring_layout(G, k=0.3, iterations=50)
    
    # 绘制网络图
    nx.draw_networkx_nodes(G, pos, node_color=node_colors, node_size=node_sizes, edgecolors='black', linewidths=1)
    
    # 绘制边，使用增强的粗细对比
    for i, (u, v, w) in enumerate(G.edges(data='weight')):
        # 使用计算好的edge_widths
        width = edge_widths[i] if i < len(edge_widths) else 3
        nx.draw_networkx_edges(G, pos, edgelist=[(u, v)], width=width, alpha=0.7,
                              edge_color='gray', arrows=True, arrowsize=15)
    
    # 添加节点标签
    labels = {i: f"{i+1}" for i in range(len(colors))}
    nx.draw_networkx_labels(G, pos, labels=labels, font_size=12, font_weight='bold')
    
    # 添加权重标签
    edge_labels = {(u, v): f"{adjacency_matrix[u][v]:.0f}" for u, v in G.edges()}
    nx.draw_networkx_edge_labels(G, pos, edge_labels=edge_labels, font_size=10)
    
    # 添加颜色图例
    legend_elements = []
    for i, color in enumerate(colors):
        r, g, b = map(int, color)
        color_patch = plt.Line2D([0], [0], marker='o', color='w', markerfacecolor=(r/255.0, g/255.0, b/255.0),
                                markersize=15, label=f'颜色 {i+1}: RGB{tuple(map(int, color))}')
        legend_elements.append(color_patch)
    
    plt.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.2, 1.0))
    
    plt.title("颜色相关度网络\n(线条粗细表示共现次数)", fontsize=16)
    plt.axis('off')
    plt.tight_layout()
    
    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        return output_path
    else:
        return plt.gcf()

# 添加networkx导入
import networkx as nx 