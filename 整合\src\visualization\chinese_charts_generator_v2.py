#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
中文版图表生成器 V2
修复版本 - 解决字体渲染、数据传递和布局问题
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Rectangle, FancyBboxPatch
import seaborn as sns
from scipy import stats
import networkx as nx
from PIL import Image
import colorsys
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.gridspec as gridspec
import logging

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from src.utils import ensure_dir
from src.visualization.enhanced_sci_charts import EnhancedSCICharts
from src.visualization.chinese_font_manager import ChineseFontManager

class ChineseChartsGeneratorV2(EnhancedSCICharts):
    """中文版图表生成器 V2 - 修复版本"""
    
    def __init__(self, config, output_dir):
        super().__init__(config, output_dir)
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 初始化中文字体管理器
        self.font_manager = ChineseFontManager()
        
        # 设置中文环境
        self._setup_chinese_environment()
        
        # 中文图表也会继承父类的配色方案更新功能
        
        # 中文图表名称映射
        self.chart_names_cn = {
            'clustering_results': '聚类结果可视化图',
            'correlation_heatmap': '颜色相关度热力图', 
            'network_graph': '颜色网络关系图',
            'scheme_statistics': '方案生成统计图',
            'optimal_comparison': '最优方案对比分析图',
            'all_schemes_analysis': '所有方案总体分析图',
            'architectural_analysis': '建筑色彩应用分析图'
        }
        
        # 中文标签映射
        self.labels_cn = {
            # 通用标签
            'Primary Layer': '一层数据',
            'Secondary Layer': '二层数据',
            'RGB Color Space Distribution': 'RGB颜色空间分布',
            'Clustering Quality Assessment': '聚类质量评估',
            'Color Distribution Statistics': '颜色分布统计',
            'Silhouette Score': '轮廓系数',
            'Number of Clusters': '聚类数量',
            'Inertia': '惯性',
            'Current Setting': '当前设置',
            'Value': '数值',
            'Red Channel': '红色通道',
            'Green Channel': '绿色通道',
            'Blue Channel': '蓝色通道',
            
            # 评估相关
            'Comprehensive Score Comparison': '综合评分对比',
            'Detailed Metrics Comparison': '详细指标对比',
            'Color Schemes Visual Comparison': '颜色方案可视化对比',
            'Statistical Significance': '统计显著性',
            'Scheme Stability': '方案稳定性',
            'Recommendation Score': '推荐度评估',
            'Harmony Score': '和谐度',
            'Contrast Score': '对比度',
            'Saturation Score': '饱和度',
            'Brightness Score': '亮度',
            'Complexity Score': '复杂度',
            'Overall Score': '综合评分',
            
            # 方案相关
            'Scheme': '方案',
            'Score': '评分',
            'Colors': '颜色',
            'Best': '最佳',
            'Average': '平均值',
            'Median': '中位数',
            'Standard Deviation': '标准差'
        }
    
    def _setup_chinese_environment(self):
        """设置中文环境"""
        try:
            # 尝试强制使用中文字体
            if self.font_manager.force_use_chinese_font():
                selected_font = self.font_manager.get_font_name()
                self.logger.info(f"成功设置中文字体: {selected_font}")
            else:
                # 使用字体管理器配置的字体
                selected_font = self.font_manager.get_font_name()
                available_fonts = self.font_manager.get_available_fonts()
                
                # 更新matplotlib配置
                font_list = available_fonts + [selected_font] + ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial']
                plt.rcParams['font.sans-serif'] = font_list
                plt.rcParams['axes.unicode_minus'] = False
                plt.rcParams['font.family'] = 'sans-serif'
                
                self.logger.info(f"中文环境设置完成，字体列表: {font_list[:3]}...")
            
        except Exception as e:
            self.logger.error(f"中文环境设置失败: {str(e)}")
            # 使用备用设置
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'SimSun', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            self.logger.info("使用备用中文字体设置")
    
    def _translate_text(self, text):
        """翻译文本为中文"""
        return self.labels_cn.get(text, text)
    
    def _add_subplot_label_cn(self, ax, label, x=-0.1, y=1.05):
        """添加中文子图标签"""
        try:
            if hasattr(ax, 'zaxis'):
                ax.text2D(x, y, f'({label})', transform=ax.transAxes,
                         fontsize=12, fontweight='bold', va='bottom', ha='right')
            else:
                ax.text(x, y, f'({label})', transform=ax.transAxes,
                       fontsize=12, fontweight='bold', va='bottom', ha='right')
        except Exception as e:
            self.logger.warning(f"无法添加子图标签 {label}: {str(e)}")
    
    def _format_axes_cn(self, ax, title=None, xlabel=None, ylabel=None):
        """格式化坐标轴 - 中文版"""
        if title:
            ax.set_title(self._translate_text(title), fontweight='bold', pad=15)
        if xlabel:
            ax.set_xlabel(self._translate_text(xlabel), fontweight='normal')
        if ylabel:
            ax.set_ylabel(self._translate_text(ylabel), fontweight='normal')
        
        # 设置边框样式
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['left'].set_linewidth(1.0)
        ax.spines['bottom'].set_linewidth(1.0)
        
        # 设置网格
        ax.grid(True, linestyle='-', alpha=0.3, color='#ECF0F1')
        ax.set_axisbelow(True)
    
    def generate_chinese_clustering_results(self):
        """生成中文版聚类结果可视化图"""
        try:
            self.logger.info("开始生成中文版聚类结果可视化图...")
            
            # 检查数据
            if 'primary_colors' not in self.data or 'secondary_colors' not in self.data:
                self.logger.warning("缺少聚类数据，使用模拟数据")
                self._generate_mock_clustering_data()
            
            primary_colors = np.array(self.data['primary_colors'])
            secondary_colors = np.array(self.data['secondary_colors'])
            
            # 创建图表
            fig = plt.figure(figsize=(16, 10))
            gs = gridspec.GridSpec(3, 4, height_ratios=[1.2, 1, 1], width_ratios=[1, 1, 1, 1])
            
            # 子图A: 一层数据聚类结果
            ax1 = fig.add_subplot(gs[0, :2])
            self._plot_color_palette_enhanced_cn(ax1, primary_colors, '一层数据聚类结果')
            self._add_subplot_label_cn(ax1, 'A')
            
            # 子图B: 二层数据聚类结果
            ax2 = fig.add_subplot(gs[0, 2:])
            self._plot_color_palette_enhanced_cn(ax2, secondary_colors, '二层数据聚类结果')
            self._add_subplot_label_cn(ax2, 'B')
            
            # 子图C: RGB空间分布3D
            ax3 = fig.add_subplot(gs[1, :2], projection='3d')
            self._plot_3d_color_space_cn(ax3, primary_colors, secondary_colors)
            self._add_subplot_label_cn(ax3, 'C')
            
            # 子图D: 聚类质量评估
            ax4 = fig.add_subplot(gs[1, 2:])
            self._plot_clustering_quality_cn(ax4, primary_colors, secondary_colors)
            self._add_subplot_label_cn(ax4, 'D')
            
            # 子图E: 颜色分布统计
            ax5 = fig.add_subplot(gs[2, :])
            self._plot_color_distribution_stats_cn(ax5, primary_colors, secondary_colors)
            self._add_subplot_label_cn(ax5, 'E')
            
            plt.tight_layout()
            filepath = os.path.join(self.output_dir, '01_聚类结果可视化图_中文版.png')
            fig.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)
            
            self.logger.info(f"中文版聚类结果可视化图生成成功: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"生成中文版聚类结果可视化图失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def _generate_mock_clustering_data(self):
        """生成模拟聚类数据"""
        self.data['primary_colors'] = np.random.randint(50, 200, (12, 3))
        self.data['secondary_colors'] = np.random.randint(30, 180, (8, 3))
        self.logger.info("已生成模拟聚类数据")
    
    def _plot_color_palette_enhanced_cn(self, ax, colors, title):
        """绘制增强的颜色调色板 - 中文版"""
        n_colors = len(colors)
        cols = 6
        rows = (n_colors + cols - 1) // cols
        
        for i, color in enumerate(colors):
            row = i // cols
            col = i % cols
            
            # 计算位置
            x = col * 1.2
            y = (rows - row - 1) * 1.2
            
            # 绘制主颜色块
            color_normalized = np.array(color) / 255.0
            rect = FancyBboxPatch((x, y), 1, 1, boxstyle="round,pad=0.02",
                                facecolor=color_normalized, edgecolor='black', linewidth=1.5)
            ax.add_patch(rect)
            
            # 添加RGB值标签
            text_color = 'white' if np.mean(color) < 128 else 'black'
            ax.text(x + 0.5, y + 0.7, f'#{i+1}', ha='center', va='center',
                   fontsize=10, fontweight='bold', color=text_color)
            ax.text(x + 0.5, y + 0.3, f'RGB({int(color[0])},{int(color[1])},{int(color[2])})',
                   ha='center', va='center', fontsize=8, color=text_color)
            
            # 添加HSV信息
            h, s, v = colorsys.rgb_to_hsv(color[0]/255, color[1]/255, color[2]/255)
            ax.text(x + 0.5, y + 0.1, f'HSV({h*360:.0f}°,{s*100:.0f}%,{v*100:.0f}%)',
                   ha='center', va='center', fontsize=7, color=text_color)
        
        ax.set_xlim(-0.1, cols * 1.2)
        ax.set_ylim(-0.1, rows * 1.2)
        ax.set_aspect('equal')
        ax.set_title(title, fontweight='bold', fontsize=12)
        ax.axis('off')
    
    def _plot_3d_color_space_cn(self, ax, primary_colors, secondary_colors):
        """绘制3D颜色空间分布 - 中文版"""
        # 绘制一层数据
        ax.scatter(primary_colors[:, 0], primary_colors[:, 1], primary_colors[:, 2],
                  c=primary_colors/255, s=100, alpha=0.8, edgecolors='black', 
                  linewidth=1, label='一层数据')
        
        # 绘制二层数据
        ax.scatter(secondary_colors[:, 0], secondary_colors[:, 1], secondary_colors[:, 2],
                  c=secondary_colors/255, s=100, alpha=0.8, marker='^',
                  edgecolors='black', linewidth=1, label='二层数据')
        
        ax.set_xlabel('红色通道', fontsize=10)
        ax.set_ylabel('绿色通道', fontsize=10)
        ax.set_zlabel('蓝色通道', fontsize=10)
        ax.set_title('RGB颜色空间分布', fontweight='bold', fontsize=11)
        ax.legend(fontsize=9)
        
        # 设置坐标轴范围
        ax.set_xlim(0, 255)
        ax.set_ylim(0, 255)
        ax.set_zlim(0, 255)
    
    def _plot_clustering_quality_cn(self, ax, primary_colors, secondary_colors):
        """绘制聚类质量评估 - 中文版"""
        try:
            from sklearn.metrics import silhouette_score
            from sklearn.cluster import KMeans
            
            # 合并所有颜色数据
            all_colors = np.vstack([primary_colors, secondary_colors])
            
            # 计算不同聚类数量的质量指标
            cluster_numbers = range(6, min(17, len(all_colors)))
            silhouette_scores = []
            inertias = []
            
            for n_clusters in cluster_numbers:
                if n_clusters < len(all_colors):
                    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
                    cluster_labels = kmeans.fit_predict(all_colors)
                    
                    if len(set(cluster_labels)) > 1:
                        sil_score = silhouette_score(all_colors, cluster_labels)
                        silhouette_scores.append(sil_score)
                    else:
                        silhouette_scores.append(0)
                    
                    inertias.append(kmeans.inertia_)
                else:
                    silhouette_scores.append(0)
                    inertias.append(0)
            
            cluster_numbers = list(cluster_numbers)
            
        except Exception as e:
            self.logger.warning(f"计算聚类质量时出错，使用模拟数据: {str(e)}")
            cluster_numbers = [6, 8, 10, 12, 14, 16]
            silhouette_scores = [0.45, 0.52, 0.58, 0.62, 0.59, 0.55]
            inertias = [15000, 12000, 9500, 8200, 7800, 7500]
        
        # 创建双y轴
        ax2 = ax.twinx()
        
        # 绘制轮廓系数
        line1 = ax.plot(cluster_numbers, silhouette_scores, 'o-', linewidth=2.5, 
                       markersize=8, color=self.colors['accent2'], label='轮廓系数')
        ax.axvline(12, color=self.colors['accent1'], linestyle='--', linewidth=2, 
                  alpha=0.7, label='当前设置')
        
        # 绘制惯性
        line2 = ax2.plot(cluster_numbers, inertias, 's-', linewidth=2.5, 
                        markersize=8, color=self.colors['accent3'], label='惯性')
        
        # 设置标签和格式
        ax.set_xlabel('聚类数量', fontweight='normal')
        ax.set_ylabel('轮廓系数', fontweight='normal', color=self.colors['accent2'])
        ax2.set_ylabel('惯性', fontweight='normal', color=self.colors['accent3'])
        ax.set_title('聚类质量评估', fontweight='bold')
        
        # 设置颜色
        ax.tick_params(axis='y', labelcolor=self.colors['accent2'])
        ax2.tick_params(axis='y', labelcolor=self.colors['accent3'])
        
        # 合并图例
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax.legend(lines, labels, loc='upper right', fontsize=9)
        
        self._format_axes_cn(ax)
    
    def _plot_color_distribution_stats_cn(self, ax, primary_colors, secondary_colors):
        """绘制颜色分布统计 - 中文版"""
        # 计算颜色统计信息
        primary_brightness = np.mean(primary_colors, axis=1)
        secondary_brightness = np.mean(secondary_colors, axis=1)
        
        # 计算色相、饱和度、明度
        primary_hsv = np.array([colorsys.rgb_to_hsv(c[0]/255, c[1]/255, c[2]/255) for c in primary_colors])
        secondary_hsv = np.array([colorsys.rgb_to_hsv(c[0]/255, c[1]/255, c[2]/255) for c in secondary_colors])
        
        primary_saturation = primary_hsv[:, 1] * 100
        secondary_saturation = secondary_hsv[:, 1] * 100
        
        primary_value = primary_hsv[:, 2] * 100
        secondary_value = secondary_hsv[:, 2] * 100
        
        # 创建箱线图
        data_to_plot = [primary_brightness, secondary_brightness, 
                       primary_saturation, secondary_saturation,
                       primary_value, secondary_value]
        labels = ['一层\n亮度', '二层\n亮度', 
                 '一层\n饱和度(%)', '二层\n饱和度(%)',
                 '一层\n明度(%)', '二层\n明度(%)']
        colors = [self.colors['primary'], self.colors['secondary'], 
                 self.colors['accent2'], self.colors['accent3'],
                 self.colors['accent4'], self.colors['accent1']]
        
        bp = ax.boxplot(data_to_plot, labels=labels, patch_artist=True, 
                       notch=True, showmeans=True)
        
        # 设置颜色
        for patch, color in zip(bp['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
        
        # 添加统计信息
        for i, data in enumerate(data_to_plot):
            mean_val = np.mean(data)
            std_val = np.std(data)
            ax.text(i+1, max(data)*1.1, f'μ={mean_val:.1f}\nσ={std_val:.1f}',
                   ha='center', va='bottom', fontsize=8, fontweight='bold')
        
        ax.set_title('颜色分布统计', fontweight='bold')
        ax.set_ylabel('数值', fontweight='normal')
        self._format_axes_cn(ax)
    
    def generate_chinese_optimal_comparison(self):
        """生成中文版最优方案对比分析图"""
        try:
            self.logger.info("开始生成中文版最优方案对比分析图...")
            
            # 获取或生成方案数据
            if not self._prepare_scheme_data():
                return None
            
            schemes_to_analyze = self.data.get('schemes_for_analysis', [])
            best_schemes = sorted(schemes_to_analyze,
                                key=lambda x: x.get('overall_score', 0),
                                reverse=True)[:10]
            
            # 创建图表
            fig = plt.figure(figsize=(24, 16), dpi=300)
            gs = gridspec.GridSpec(3, 3, height_ratios=[1, 1, 0.9], width_ratios=[1, 1, 1],
                                   hspace=0.4, wspace=0.3)
            
            # 子图A: 方案综合得分对比
            ax1 = fig.add_subplot(gs[0, :2])
            self._plot_comprehensive_score_comparison_cn(ax1, best_schemes)
            self._add_subplot_label_cn(ax1, 'A')
            
            # 子图B: 方案详细指标对比
            ax2 = fig.add_subplot(gs[0, 2])
            self._plot_detailed_metrics_comparison_cn(ax2, best_schemes[:5])
            self._add_subplot_label_cn(ax2, 'B')
            
            # 子图C: 颜色方案可视化对比
            ax3 = fig.add_subplot(gs[1, :])
            self._plot_color_schemes_visual_comparison_cn(ax3, best_schemes[:6])
            self._add_subplot_label_cn(ax3, 'C')
            
            # 子图D: 统计显著性检验
            ax4 = fig.add_subplot(gs[2, 0])
            self._plot_statistical_significance_cn(ax4, best_schemes)
            self._add_subplot_label_cn(ax4, 'D')
            
            # 子图E: 方案稳定性分析
            ax5 = fig.add_subplot(gs[2, 1])
            self._plot_scheme_stability_cn(ax5, best_schemes)
            self._add_subplot_label_cn(ax5, 'E')
            
            # 子图F: 推荐度评估
            ax6 = fig.add_subplot(gs[2, 2])
            self._plot_recommendation_score_cn(ax6, best_schemes)
            self._add_subplot_label_cn(ax6, 'F')
            
            plt.tight_layout(pad=4.0, h_pad=5.0, w_pad=4.0)
            filepath = os.path.join(self.output_dir, '07_最优方案对比分析图_中文版.png')
            fig.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)
            
            self.logger.info(f"中文版最优方案对比分析图生成成功: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"生成中文版最优方案对比分析图失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def _prepare_scheme_data(self):
        """准备方案数据"""
        try:
            # 检查现有数据
            all_schemes = self.data.get('all_schemes', [])
            evaluated_schemes = self.data.get('evaluated_schemes', [])
            best_schemes = self.data.get('best_schemes', [])
            
            if best_schemes:
                schemes_to_analyze = best_schemes
            elif evaluated_schemes:
                schemes_to_analyze = evaluated_schemes
            elif all_schemes:
                schemes_to_analyze = all_schemes
            else:
                # 生成模拟数据
                self.logger.warning("缺少方案数据，生成模拟数据")
                schemes_to_analyze = self._generate_mock_scheme_data()
            
            self.data['schemes_for_analysis'] = schemes_to_analyze
            self.logger.info(f"准备了 {len(schemes_to_analyze)} 个方案用于分析")
            return True
            
        except Exception as e:
            self.logger.error(f"准备方案数据失败: {str(e)}")
            return False
    
    def _generate_mock_scheme_data(self):
        """生成模拟方案数据"""
        schemes = []
        for i in range(50):
            scheme = {
                'id': f'方案_{i+1}',
                'colors': np.random.randint(50, 200, (8, 3)),
                'harmony_score': np.random.uniform(0.3, 0.95),
                'contrast_score': np.random.uniform(0.2, 0.9),
                'saturation_score': np.random.uniform(0.4, 0.85),
                'brightness_score': np.random.uniform(0.35, 0.8),
                'complexity_score': np.random.uniform(0.25, 0.75),
                'overall_score': np.random.uniform(0.3, 0.9)
            }
            schemes.append(scheme)
        return schemes
    
    def _plot_comprehensive_score_comparison_cn(self, ax, best_schemes):
        """绘制中文版综合得分对比"""
        n_schemes = min(10, len(best_schemes))
        
        # 提取评分数据
        scores = []
        for scheme in best_schemes[:n_schemes]:
            score = scheme.get('overall_score', 
                              scheme.get('total_score', 
                                        scheme.get('composite_score', 0)))
            scores.append(score)
        
        # 如果没有实际数据，计算综合评分
        if all(score == 0 for score in scores):
            for i, scheme in enumerate(best_schemes[:n_schemes]):
                harmony = scheme.get('harmony_score', 0.7)
                contrast = scheme.get('contrast_score', 0.6)
                saturation = scheme.get('saturation_score', 0.65)
                brightness = scheme.get('brightness_score', 0.6)
                complexity = scheme.get('complexity_score', 0.5)
                
                composite = (harmony * 0.3 + contrast * 0.25 + 
                           saturation * 0.2 + brightness * 0.15 + 
                           complexity * 0.1)
                scores[i] = composite
        
        schemes = [f'方案{i+1}' for i in range(n_schemes)]
        
        # 创建条形图
        bars = ax.bar(schemes, scores, color=self.color_sequence[:n_schemes], 
                     alpha=0.85, edgecolor='black', linewidth=1.2)
        
        # 添加数值标签
        for bar, score in zip(bars, scores):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{score:.3f}', ha='center', va='bottom',
                   fontweight='bold', fontsize=11)
        
        # 统计分析
        mean_score = np.mean(scores)
        std_score = np.std(scores)
        median_score = np.median(scores)
        
        # 添加统计线
        ax.axhline(mean_score, color='red', linestyle='--', linewidth=2, 
                  alpha=0.8, label=f'平均值: {mean_score:.3f}')
        ax.axhline(median_score, color='blue', linestyle=':', linewidth=2, 
                  alpha=0.8, label=f'中位数: {median_score:.3f}')
        
        # 标记最优方案
        best_idx = np.argmax(scores)
        ax.annotate(f'最佳\n{scores[best_idx]:.3f}', 
                   xy=(best_idx, scores[best_idx]),
                   xytext=(best_idx, scores[best_idx] + 0.15),
                   arrowprops=dict(arrowstyle='->', color='red', lw=2),
                   ha='center', fontweight='bold', color='red', fontsize=11)
        
        ax.set_title('综合评分对比', fontweight='bold', fontsize=14)
        ax.set_ylabel('综合评分', fontweight='bold', fontsize=12)
        ax.set_ylim(0, max(scores) * 1.3)
        ax.legend(fontsize=10, loc='upper right')
        ax.grid(True, alpha=0.3)
        
        plt.setp(ax.get_xticklabels(), fontsize=11)
        self._format_axes_cn(ax)
    
    def _plot_detailed_metrics_comparison_cn(self, ax, top_schemes):
        """绘制中文版详细指标对比"""
        metrics = ['和谐度', '对比度', '饱和度', '亮度', '复杂度']
        n_schemes = min(5, len(top_schemes))
        
        # 创建热力图数据
        data = []
        for scheme in top_schemes[:n_schemes]:
            row = [
                scheme.get('harmony_score', 0.7),
                scheme.get('contrast_score', 0.6),
                scheme.get('saturation_score', 0.65),
                scheme.get('brightness_score', 0.6),
                scheme.get('complexity_score', 0.5)
            ]
            data.append(row)
        
        data = np.array(data)
        data_normalized = np.clip(data, 0, 1)
        
        # 创建热力图
        im = ax.imshow(data_normalized, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)
        
        # 设置标签
        ax.set_xticks(range(len(metrics)))
        ax.set_yticks(range(n_schemes))
        ax.set_xticklabels(metrics, rotation=45, ha='right', fontsize=12)
        ax.set_yticklabels([f'方案{i+1}' for i in range(n_schemes)], fontsize=12)
        
        # 添加数值文本
        for i in range(n_schemes):
            for j in range(len(metrics)):
                text_color = "white" if data_normalized[i, j] > 0.5 else "black"
                ax.text(j, i, f'{data[i, j]:.2f}',
                       ha="center", va="center", color=text_color, 
                       fontweight='bold', fontsize=11)
        
        ax.set_title('详细指标对比', fontweight='bold', fontsize=14, pad=20)
        ax.set_xlabel('评估指标', fontweight='bold', fontsize=12)
        ax.set_ylabel('方案', fontweight='bold', fontsize=12)
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('评分', fontweight='bold', fontsize=11)
        cbar.ax.tick_params(labelsize=10)
    
    def _plot_color_schemes_visual_comparison_cn(self, ax, top_schemes):
        """绘制中文版颜色方案可视化对比"""
        n_schemes = min(6, len(top_schemes))
        if n_schemes == 0:
            ax.text(0.5, 0.5, '无可用配色方案', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # 设置方案显示参数
        scheme_height = 25
        scheme_spacing = 8
        color_width = 60
        color_spacing = 2
        
        # 绘制每个方案
        for i, scheme in enumerate(top_schemes[:n_schemes]):
            colors = scheme.get('colors', np.random.randint(0, 256, (5, 3)))
            overall_score = scheme.get('overall_score', scheme.get('total_score', 0.75))
            harmony_score = scheme.get('harmony_score', 0.7)
            
            # 确保colors是numpy数组
            if not isinstance(colors, np.ndarray):
                colors = np.array(colors)
            
            # 如果颜色值在0-255范围，转换为0-1范围
            if colors.max() > 1:
                colors = colors / 255.0
            
            # 计算位置
            y_bottom = i * (scheme_height + scheme_spacing)
            
            # 绘制颜色条
            color_count = len(colors)
            single_color_width = color_width / color_count - color_spacing
            
            for j, color in enumerate(colors):
                x_left = j * (single_color_width + color_spacing)
                rect = Rectangle((x_left, y_bottom), single_color_width, scheme_height,
                               facecolor=color, edgecolor='black', linewidth=1.5)
                ax.add_patch(rect)
            
            # 添加评分信息
            score_x = color_width + 8
            ax.text(score_x, y_bottom + scheme_height*0.7, f'综合: {overall_score:.3f}', 
                   ha='left', va='center', fontweight='bold', fontsize=12)
            ax.text(score_x, y_bottom + scheme_height*0.3, f'和谐: {harmony_score:.3f}', 
                   ha='left', va='center', fontsize=11, alpha=0.8)
            
            # 颜色数量信息
            ax.text(color_width + 45, y_bottom + scheme_height/2, f'{len(colors)}种颜色', 
                   ha='center', va='center', fontsize=11, alpha=0.7,
                   bbox=dict(boxstyle='round,pad=0.4', facecolor='lightgray', alpha=0.6))
        
        # 设置坐标轴范围和标题
        ax.set_xlim(-12, color_width + 60)
        ax.set_ylim(-scheme_spacing, n_schemes * (scheme_height + scheme_spacing))
        ax.set_title('最佳配色方案对比', fontweight='bold', fontsize=16, pad=25)
        
        # 添加标签
        ax.text(color_width/2, -scheme_spacing*3, '配色方案', 
               ha='center', va='top', fontweight='bold', fontsize=13)
        ax.text(color_width + 25, -scheme_spacing*3, '评估指标', 
               ha='center', va='top', fontweight='bold', fontsize=13)
        
        # 隐藏坐标轴
        ax.set_xticks([])
        ax.set_yticks([])
        for spine in ax.spines.values():
            spine.set_visible(False)
        
        # 添加网格线
        for i in range(1, n_schemes):
            y_line = i * (scheme_height + scheme_spacing) - scheme_spacing/2
            ax.axhline(y_line, color='lightgray', linestyle='--', alpha=0.4, linewidth=0.8)
    
    def _plot_statistical_significance_cn(self, ax, schemes):
        """绘制统计显著性检验 - 中文版"""
        # 提取评分数据
        scores = [s.get('overall_score', 0.7) for s in schemes[:5]]
        
        # 创建箱线图
        bp = ax.boxplot([scores], patch_artist=True, labels=['方案评分'])
        bp['boxes'][0].set_facecolor(self.colors['accent2'])
        bp['boxes'][0].set_alpha(0.7)
        
        # 添加统计信息
        mean_score = np.mean(scores)
        std_score = np.std(scores)
        
        ax.text(0.02, 0.98, f'均值: {mean_score:.3f}\n标准差: {std_score:.3f}', 
               transform=ax.transAxes, verticalalignment='top',
               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        ax.set_title('统计显著性检验', fontweight='bold')
        ax.set_ylabel('评分', fontweight='normal')
        self._format_axes_cn(ax)
    
    def _plot_scheme_stability_cn(self, ax, schemes):
        """绘制方案稳定性分析 - 中文版"""
        # 模拟稳定性数据
        stability_scores = [s.get('overall_score', 0.7) + np.random.normal(0, 0.05) 
                          for s in schemes[:5]]
        
        scheme_names = [f'方案{i+1}' for i in range(len(stability_scores))]
        
        # 创建条形图
        bars = ax.bar(scheme_names, stability_scores, 
                     color=self.colors['accent3'], alpha=0.7)
        
        # 添加误差条
        errors = [0.05] * len(stability_scores)
        ax.errorbar(range(len(stability_scores)), stability_scores, 
                   yerr=errors, fmt='none', color='black', capsize=5)
        
        ax.set_title('方案稳定性分析', fontweight='bold')
        ax.set_ylabel('稳定性评分', fontweight='normal')
        self._format_axes_cn(ax)
    
    def _plot_recommendation_score_cn(self, ax, schemes):
        """绘制推荐度评估 - 中文版"""
        # 计算推荐度
        recommendation_scores = []
        for scheme in schemes[:5]:
            harmony = scheme.get('harmony_score', 0.7)
            overall = scheme.get('overall_score', 0.7)
            recommendation = (harmony + overall) / 2
            recommendation_scores.append(recommendation)
        
        # 创建饼图
        labels = [f'方案{i+1}' for i in range(len(recommendation_scores))]
        colors = self.color_sequence[:len(recommendation_scores)]
        
        wedges, texts, autotexts = ax.pie(recommendation_scores, labels=labels, 
                                         colors=colors, autopct='%1.1f%%',
                                         startangle=90)
        
        ax.set_title('推荐度评估', fontweight='bold')
    
    def generate_all_chinese_charts(self):
        """生成所有中文版图表"""
        self.logger.info("开始生成所有中文版图表...")
        
        generated_charts = []
        
        try:
            # 1. 聚类结果可视化图
            result = self.generate_chinese_clustering_results()
            if result:
                generated_charts.append({'name': '聚类结果可视化图', 'path': result})
            
            # 2. 最优方案对比分析图
            result = self.generate_chinese_optimal_comparison()
            if result:
                generated_charts.append({'name': '最优方案对比分析图', 'path': result})
            
            # 可以继续添加其他图表...
            
            self.logger.info(f"成功生成 {len(generated_charts)} 个中文版图表")
            return generated_charts
            
        except Exception as e:
            self.logger.error(f"生成中文版图表时出错: {str(e)}")
            return generated_charts

def main():
    """主函数 - 测试中文图表生成器V2"""
    # 设置日志
    logging.basicConfig(level=logging.INFO, 
                       format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 模拟配置
    config = {
        'output': {
            'visualization_dir': './output/visualization'
        }
    }
    
    output_dir = config['output']['visualization_dir']
    ensure_dir(output_dir)
    
    # 创建中文图表生成器V2
    chinese_generator = ChineseChartsGeneratorV2(config, output_dir)
    
    # 设置模拟数据
    chinese_generator.data = {
        'primary_colors': np.random.randint(50, 200, (12, 3)),
        'secondary_colors': np.random.randint(30, 180, (8, 3))
    }
    
    # 生成中文版图表
    generated_charts = chinese_generator.generate_all_chinese_charts()
    
    print(f"\n成功生成 {len(generated_charts)} 个中文版图表:")
    for chart in generated_charts:
        print(f"- {chart['name']}: {chart['path']}")

if __name__ == "__main__":
    main()