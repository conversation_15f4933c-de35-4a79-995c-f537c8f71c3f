# 建筑立面色彩优化系统

本项目是一个基于图像聚类算法的历史街区新建建筑立面色彩优化系统。该系统通过分析街景图像，提取主要色彩，并生成符合历史风貌的建筑立面色彩方案。

## 项目结构

```
├── config/                # 配置文件目录
│   └── settings.json      # 系统配置参数
├── data/                  # 数据目录
│   ├── primary/           # 一层数据（主要立面街景图像）
│   └── secondary/         # 二层数据（其他三个立面街景图像）
├── src/                   # 源代码目录
│   ├── data_processing/   # 数据处理模块
│   │   ├── __init__.py
│   │   ├── clustering.py  # 聚类算法
│   │   └── correlation.py # 颜色相关度计算
│   ├── color_generation/  # 色彩生成模块
│   │   ├── __init__.py
│   │   ├── gradient.py    # 渐变生成
│   │   └── insertion.py   # 二层数据颜色插入
│   ├── evaluation/        # 评估系统
│   │   ├── __init__.py
│   │   └── evaluator.py   # 评估算法
│   ├── __init__.py
│   ├── main.py            # 主程序入口
│   └── utils.py           # 工具函数
└── output/                # 输出目录
    ├── clustering/        # 聚类结果
    ├── correlation/       # 颜色相关度结果
    ├── color_schemes/     # 色彩方案
    ├── evaluation/        # 评估结果
    └── visualization/     # 可视化结果
```

## 使用方法

1. 数据准备：
   - 将主要立面（一层数据）的街景图像放入 `data/primary` 目录
   - 将其他三个立面（二层数据）的街景图像放入 `data/secondary` 目录

2. 运行系统：
   ```
   python src/main.py
   ```

3. 查看结果：
   - 聚类结果：`output/clustering`
   - 颜色相关度结果：`output/correlation`
   - 色彩方案：`output/color_schemes`
   - 评估结果：`output/evaluation`
   - 可视化结果：`output/visualization`

## 系统流程

1. 数据处理：
   - 对一层数据进行聚类，提取12个主要颜色
   - 计算颜色邻接矩阵，获取相关度最高的三个颜色
   - 对二层数据进行聚类，提取12个主要颜色

2. 色彩生成：
   - 对一层数据的三个主要颜色进行噪声扰动渐变
   - 按照7:3的比例将二层数据颜色插入到渐变方案中
   - 生成多种色彩方案

3. 评估系统：
   - 根据欧氏距离、相邻颜色和谐度、历史颜色和谐度三个维度评估方案
   - 输出最佳方案和评分数据

## 配置参数

系统参数可在 `config/settings.json` 文件中进行配置，包括：

- 聚类数量
- 颜色相关度阈值
- 一层和二层数据的比例
- 噪声扰动参数
- 评估权重参数

## 依赖库

- numpy
- scikit-learn
- PIL (Pillow)
- pandas
- matplotlib
- colorsys 
所有图表保存在 output/visualization/enhanced_sci_charts/ 目录：

01_clustering_results_sci.png - 聚类结果可视化
02_correlation_heatmap_sci.png - 颜色相关度热力图
03_network_graph_sci.png - 颜色网络关系图
04_gradient_process_sci.png - 渐变生成过程图
05_scheme_statistics_sci.png - 方案生成统计图
06_evaluation_radar_sci.png - 多维度评估雷达图
07_optimal_comparison_sci.png - 最优方案对比分析图