2025-08-02 17:41:14,215 - __main__ - INFO - 🚀 建筑立面色彩优化系统启动
2025-08-02 17:41:14,217 - __main__ - INFO - 📝 日志文件: logs\system_20250802_174114.log
2025-08-02 17:41:14,220 - __main__ - INFO - 📁 目录结构创建完成
2025-08-02 17:41:14,221 - __main__ - INFO - 🏗️ 开始执行完整工作流程
2025-08-02 17:41:14,222 - __main__ - INFO - 🔄 步骤1: 开始图像预处理
2025-08-02 17:41:20,824 - __main__ - INFO - ✅ 步骤1: 图像预处理完成
2025-08-02 17:41:20,827 - __main__ - INFO - 🔄 步骤2: 开始聚类分析
2025-08-02 17:41:28,962 - __main__ - INFO - ✅ 步骤2: 聚类分析完成
2025-08-02 17:41:28,962 - __main__ - INFO - 🔄 步骤3: 开始相关度分析
2025-08-02 17:43:41,575 - __main__ - INFO - ✅ 步骤3: 相关度分析完成
2025-08-02 17:43:41,576 - __main__ - INFO - 🔄 步骤4: 开始色彩生成
2025-08-02 17:44:08,875 - __main__ - INFO - ✅ 步骤4: 色彩生成完成
2025-08-02 17:44:08,878 - __main__ - INFO - 🔄 步骤5: 开始方案评估
2025-08-02 17:44:10,230 - __main__ - INFO - ✅ 步骤5: 方案评估完成
2025-08-02 17:44:10,230 - __main__ - INFO - 🔄 步骤6: 开始可视化生成
2025-08-02 17:44:10,859 - __main__ - ERROR - 可视化生成执行失败: 'EnhancedSCICharts' object has no attribute 'generate_all_charts'
2025-08-02 17:44:10,860 - __main__ - INFO - 🔄 步骤7: 开始数据导出
2025-08-02 17:44:10,862 - __main__ - ERROR - 数据导出模块导入失败: cannot import name 'DataExporter' from 'src.data_export' (D:\桌面\颜色相关度\整合\src\data_export.py)
2025-08-02 17:44:10,864 - __main__ - INFO - 🎉 完整工作流程执行成功，耗时: 176.64秒
