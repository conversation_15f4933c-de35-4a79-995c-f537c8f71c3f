#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
建筑立面色彩优化系统 - 主入口文件
版本: 2.0.0 (重构版)

完整的工作流程：
1. 图像预处理 - 阴影去除、光照归一化、色彩增强
2. 数据处理 - 聚类分析、颜色提取
3. 相关度计算 - 颜色邻接矩阵、共现分析
4. 色彩生成 - 渐变生成、插入生成
5. 评估系统 - 多维度评估、最佳方案选择
6. 可视化 - SCI标准图表、中文支持
7. 数据导出 - 结果保存、报告生成
"""

import os
import sys
import json
import logging
import time
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.abspath('.'))

class ColorOptimizationSystem:
    """建筑立面色彩优化系统主类"""
    
    def __init__(self, config_path="./config/settings.json"):
        """
        初始化系统
        
        Args:
            config_path (str): 配置文件路径
        """
        self.config = self.load_config(config_path)
        self.setup_logging()
        self.setup_directories()
        
        # 存储各步骤结果
        self.clustering_results = {}
        self.correlation_results = {}
        self.color_schemes = {}
        self.evaluation_results = {}
        
    def load_config(self, config_path):
        """
        加载配置文件
        
        Args:
            config_path (str): 配置文件路径
            
        Returns:
            dict: 配置字典
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"✅ 配置文件加载成功: {config_path}")
            return config
        except FileNotFoundError:
            print(f"❌ 配置文件不存在: {config_path}")
            sys.exit(1)
        except json.JSONDecodeError as e:
            print(f"❌ 配置文件格式错误: {e}")
            sys.exit(1)
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            sys.exit(1)
    
    def setup_logging(self):
        """设置日志系统"""
        # 创建日志目录
        log_dir = Path("./logs")
        log_dir.mkdir(exist_ok=True)
        
        # 生成日志文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = log_dir / f"system_{timestamp}.log"
        
        # 配置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("🚀 建筑立面色彩优化系统启动")
        self.logger.info(f"📝 日志文件: {log_file}")
    
    def setup_directories(self):
        """创建必要的目录结构"""
        directories = [
            "./output",
            "./output/clustering",
            "./output/correlation", 
            "./output/color_schemes",
            "./output/evaluation",
            "./output/visualization",
            "./output/preprocessed_images",
            "./output/preprocessed_primary",
            "./output/preprocessed_secondary",
            "./output/exports",
            "./logs"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
        
        self.logger.info("📁 目录结构创建完成")
    
    def run_complete_workflow(self):
        """
        运行完整的工作流程
        
        Returns:
            bool: 执行成功返回True，失败返回False
        """
        self.logger.info("🏗️ 开始执行完整工作流程")
        start_time = time.time()
        
        try:
            print("\n" + "="*60)
            print("🏗️ 建筑立面色彩优化系统 - 完整工作流程")
            print("="*60)
            
            # 步骤1: 图像预处理
            print("\n📸 步骤1: 图像预处理")
            self.step1_image_preprocessing()
            
            # 步骤2: 数据处理（聚类分析）
            print("\n🔍 步骤2: 聚类分析")
            self.step2_data_processing()
            
            # 步骤3: 颜色相关度计算
            print("\n🔗 步骤3: 相关度分析")
            self.step3_correlation_analysis()
            
            # 步骤4: 色彩生成
            print("\n🎨 步骤4: 色彩生成")
            self.step4_color_generation()
            
            # 步骤5: 评估系统
            print("\n📊 步骤5: 方案评估")
            self.step5_evaluation()
            
            # 步骤6: 可视化生成
            print("\n📈 步骤6: 可视化生成")
            self.step6_visualization()
            
            # 步骤7: 数据导出
            print("\n💾 步骤7: 数据导出")
            self.step7_data_export()
            
            # 计算总执行时间
            end_time = time.time()
            execution_time = end_time - start_time
            
            print("\n" + "="*60)
            print("🎉 完整工作流程执行成功！")
            print(f"⏱️  总执行时间: {execution_time:.2f} 秒")
            print("📁 请查看 ./output/ 目录获取所有结果")
            print("="*60)
            
            self.logger.info(f"🎉 完整工作流程执行成功，耗时: {execution_time:.2f}秒")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 工作流程执行失败: {str(e)}")
            print(f"\n❌ 工作流程执行失败: {str(e)}")
            print("📝 详细错误信息请查看日志文件")
            return False
    
    def step1_image_preprocessing(self):
        """步骤1: 图像预处理"""
        self.logger.info("🔄 步骤1: 开始图像预处理")
        
        try:
            # 导入图像预处理模块
            from src.data_processing.image_preprocessing import ImagePreprocessor
            
            # 创建预处理器
            preprocessor = ImagePreprocessor(self.config.get('preprocessing', {}))
            
            # 处理主要立面图像
            primary_dir = self.config['data']['primary_dir']
            if os.path.exists(primary_dir):
                print(f"   🔄 处理主要立面图像: {primary_dir}")
                preprocessor.preprocess_directory(primary_dir, "./output/preprocessed_primary")
                print(f"   ✅ 主要立面图像处理完成")
            else:
                print(f"   ⚠️  主要立面目录不存在: {primary_dir}")
            
            # 处理次要立面图像
            secondary_dir = self.config['data']['secondary_dir']
            if os.path.exists(secondary_dir):
                print(f"   🔄 处理次要立面图像: {secondary_dir}")
                preprocessor.preprocess_directory(secondary_dir, "./output/preprocessed_secondary")
                print(f"   ✅ 次要立面图像处理完成")
            else:
                print(f"   ⚠️  次要立面目录不存在: {secondary_dir}")
            
            self.logger.info("✅ 步骤1: 图像预处理完成")
            
        except ImportError as e:
            error_msg = f"图像预处理模块导入失败: {e}"
            self.logger.error(error_msg)
            print(f"   ❌ {error_msg}")
            raise
        except Exception as e:
            error_msg = f"图像预处理执行失败: {e}"
            self.logger.error(error_msg)
            print(f"   ❌ {error_msg}")
            raise
    
    def step2_data_processing(self):
        """步骤2: 数据处理（聚类分析）"""
        self.logger.info("🔄 步骤2: 开始聚类分析")
        
        try:
            # 导入聚类分析模块
            from src.data_processing.clustering import PrimaryClusterer, SecondaryClusterer
            
            # 主要立面聚类
            print("   🔄 主要立面聚类分析...")
            primary_clusterer = PrimaryClusterer(self.config)
            primary_colors = primary_clusterer.process()
            print(f"   ✅ 主要立面聚类完成，提取 {len(primary_colors)} 个颜色")
            
            # 次要立面聚类
            print("   🔄 次要立面聚类分析...")
            secondary_clusterer = SecondaryClusterer(self.config)
            secondary_colors = secondary_clusterer.process()
            print(f"   ✅ 次要立面聚类完成，提取 {len(secondary_colors)} 个颜色")
            
            # 保存聚类结果
            self.clustering_results = {
                'primary_colors': primary_colors,
                'secondary_colors': secondary_colors
            }
            
            self.logger.info("✅ 步骤2: 聚类分析完成")
            
        except ImportError as e:
            error_msg = f"聚类分析模块导入失败: {e}"
            self.logger.error(error_msg)
            print(f"   ❌ {error_msg}")
            raise
        except Exception as e:
            error_msg = f"聚类分析执行失败: {e}"
            self.logger.error(error_msg)
            print(f"   ❌ {error_msg}")
            raise
    
    def step3_correlation_analysis(self):
        """步骤3: 颜色相关度计算"""
        self.logger.info("🔄 步骤3: 开始相关度分析")
        
        try:
            # 导入相关度分析模块
            from src.data_processing.correlation import ColorCorrelation
            
            print("   🔄 计算颜色相关度...")
            correlation_analyzer = ColorCorrelation(self.config)
            
            # 计算颜色相关度
            correlation_results = correlation_analyzer.analyze(
                self.clustering_results['primary_colors']
            )
            
            self.correlation_results = correlation_results
            print(f"   ✅ 相关度分析完成，找到 {len(correlation_results.get('top_colors', []))} 个高相关颜色")
            
            self.logger.info("✅ 步骤3: 相关度分析完成")
            
        except ImportError as e:
            error_msg = f"相关度分析模块导入失败: {e}"
            self.logger.error(error_msg)
            print(f"   ❌ {error_msg}")
            raise
        except Exception as e:
            error_msg = f"相关度分析执行失败: {e}"
            self.logger.error(error_msg)
            print(f"   ❌ {error_msg}")
            raise
    
    def step4_color_generation(self):
        """步骤4: 色彩生成"""
        self.logger.info("🔄 步骤4: 开始色彩生成")
        
        try:
            # 导入色彩生成模块
            from src.color_generation.gradient import GradientGenerator
            from src.color_generation.insertion import InsertionGenerator
            
            # 渐变生成
            print("   🔄 生成渐变色彩方案...")
            gradient_gen = GradientGenerator(self.config)
            gradient_schemes = gradient_gen.generate_schemes(
                self.correlation_results.get('top_colors', [])
            )
            print(f"   ✅ 渐变方案生成完成，共 {len(gradient_schemes)} 个方案")
            
            # 插入生成
            print("   🔄 生成插入色彩方案...")
            insertion_gen = InsertionGenerator(self.config)
            insertion_schemes = insertion_gen.generate_schemes(
                gradient_schemes,
                self.clustering_results['secondary_colors']
            )
            print(f"   ✅ 插入方案生成完成，共 {len(insertion_schemes)} 个方案")
            
            self.color_schemes = {
                'gradient_schemes': gradient_schemes,
                'insertion_schemes': insertion_schemes
            }
            
            self.logger.info("✅ 步骤4: 色彩生成完成")
            
        except ImportError as e:
            error_msg = f"色彩生成模块导入失败: {e}"
            self.logger.error(error_msg)
            print(f"   ❌ {error_msg}")
            raise
        except Exception as e:
            error_msg = f"色彩生成执行失败: {e}"
            self.logger.error(error_msg)
            print(f"   ❌ {error_msg}")
            raise

    def step5_evaluation(self):
        """步骤5: 评估系统"""
        self.logger.info("🔄 步骤5: 开始方案评估")

        try:
            # 导入评估模块
            from src.evaluation.evaluator import ColorEvaluator

            print("   🔄 评估色彩方案...")
            evaluator = ColorEvaluator(self.config)

            # 评估所有方案
            all_schemes = (self.color_schemes.get('gradient_schemes', []) +
                          self.color_schemes.get('insertion_schemes', []))

            if not all_schemes:
                raise ValueError("没有可评估的色彩方案")

            evaluation_results = evaluator.evaluate_schemes(all_schemes)

            self.evaluation_results = evaluation_results
            best_count = len(evaluation_results.get('best_schemes', []))
            print(f"   ✅ 方案评估完成，选出 {best_count} 个最佳方案")

            self.logger.info("✅ 步骤5: 方案评估完成")

        except ImportError as e:
            error_msg = f"评估系统模块导入失败: {e}"
            self.logger.error(error_msg)
            print(f"   ❌ {error_msg}")
            raise
        except Exception as e:
            error_msg = f"方案评估执行失败: {e}"
            self.logger.error(error_msg)
            print(f"   ❌ {error_msg}")
            raise

    def step6_visualization(self):
        """步骤6: 可视化生成"""
        self.logger.info("🔄 步骤6: 开始可视化生成")

        try:
            # 导入可视化模块
            from src.visualization.enhanced_sci_charts import EnhancedSCICharts

            print("   🔄 生成SCI标准图表...")

            # 准备可视化数据
            viz_data = {
                'clustering_results': self.clustering_results,
                'correlation_results': self.correlation_results,
                'color_schemes': self.color_schemes,
                'evaluation_results': self.evaluation_results
            }

            # 生成SCI标准图表
            chart_generator = EnhancedSCICharts(self.config, "./output/visualization")
            chart_results = chart_generator.generate_all_charts(viz_data)

            chart_count = len([r for r in chart_results.values() if r is not None])
            print(f"   ✅ 可视化生成完成，共生成 {chart_count} 个图表")

            self.logger.info("✅ 步骤6: 可视化生成完成")

        except ImportError as e:
            error_msg = f"可视化模块导入失败: {e}"
            self.logger.error(error_msg)
            print(f"   ❌ {error_msg}")
            # 可视化失败不应该中断整个流程
            print("   ⚠️  可视化失败，继续执行后续步骤")
        except Exception as e:
            error_msg = f"可视化生成执行失败: {e}"
            self.logger.error(error_msg)
            print(f"   ❌ {error_msg}")
            print("   ⚠️  可视化失败，继续执行后续步骤")

    def step7_data_export(self):
        """步骤7: 数据导出"""
        self.logger.info("🔄 步骤7: 开始数据导出")

        try:
            # 导入数据导出模块
            from src.data_export import DataExporter

            print("   🔄 导出分析结果...")
            exporter = DataExporter(self.config)

            # 导出所有数据
            export_data = {
                'clustering_results': self.clustering_results,
                'correlation_results': self.correlation_results,
                'color_schemes': self.color_schemes,
                'evaluation_results': self.evaluation_results
            }

            export_path = exporter.export_all_data(export_data)
            print(f"   ✅ 数据导出完成，保存至: {export_path}")

            self.logger.info("✅ 步骤7: 数据导出完成")

        except ImportError as e:
            error_msg = f"数据导出模块导入失败: {e}"
            self.logger.error(error_msg)
            print(f"   ❌ {error_msg}")
            # 数据导出失败不应该中断整个流程
            print("   ⚠️  数据导出失败，但主要分析已完成")
        except Exception as e:
            error_msg = f"数据导出执行失败: {e}"
            self.logger.error(error_msg)
            print(f"   ❌ {error_msg}")
            print("   ⚠️  数据导出失败，但主要分析已完成")

def main():
    """
    主函数 - 系统入口点

    Returns:
        bool: 执行成功返回True，失败返回False
    """
    print("🏗️ 建筑立面色彩优化系统")
    print("=" * 60)
    print("版本: 2.0.0 (重构版)")
    print("作者: 建筑色彩优化团队")
    print("=" * 60)

    try:
        # 创建系统实例
        system = ColorOptimizationSystem()

        # 运行完整工作流程
        success = system.run_complete_workflow()

        if success:
            print("\n🎉 系统运行成功完成！")
            print("\n📋 输出文件说明:")
            print("   📁 ./output/clustering/     - 聚类分析结果")
            print("   📁 ./output/correlation/    - 颜色相关度分析")
            print("   📁 ./output/color_schemes/  - 生成的色彩方案")
            print("   📁 ./output/evaluation/     - 方案评估结果")
            print("   📁 ./output/visualization/  - 可视化图表")
            print("   📁 ./output/exports/        - 导出的数据文件")
            print("   📁 ./logs/                  - 系统日志文件")
            return True
        else:
            print("\n❌ 系统运行失败！")
            print("📝 请查看日志文件获取详细错误信息")
            return False

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断了程序执行")
        return False
    except Exception as e:
        print(f"\n❌ 系统运行出错: {str(e)}")
        print("📝 详细错误信息:")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 运行主函数
    success = main()

    # 根据执行结果设置退出码
    sys.exit(0 if success else 1)
