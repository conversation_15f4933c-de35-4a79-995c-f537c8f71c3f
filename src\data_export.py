#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据导出模块

用于导出所有颜色方案数据，便于后续的图纸制作和分析

作者: AI Assistant
日期: 2024年12月
"""

import os
import json
import csv
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime
from PIL import Image, ImageDraw

class ColorSchemeDataExporter:
    """颜色方案数据导出器"""
    
    def __init__(self, output_dir="./exports"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        self.json_dir = self.output_dir / "json"
        self.csv_dir = self.output_dir / "csv"
        self.excel_dir = self.output_dir / "excel"
        self.images_dir = self.output_dir / "images"
        self.palettes_dir = self.output_dir / "palettes"
        
        for dir_path in [self.json_dir, self.csv_dir, self.excel_dir, self.images_dir, self.palettes_dir]:
            dir_path.mkdir(exist_ok=True)
    
    def export_all_data(self, data):
        """导出所有数据"""
        print("🔄 开始导出所有数据...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 导出基础数据
        self.export_basic_data(data, timestamp)
        
        # 导出颜色方案
        self.export_color_schemes(data, timestamp)
        
        # 导出评估结果
        self.export_evaluation_results(data, timestamp)
        
        # 导出相关度分析
        self.export_correlation_analysis(data, timestamp)
        
        # 导出统计数据
        self.export_statistics(data, timestamp)
        
        # 生成颜色调色板图像
        self.generate_color_palettes(data, timestamp)
        
        # 生成导出报告
        self.generate_export_report(data, timestamp)
        
        print(f"✅ 数据导出完成！文件保存在: {self.output_dir}")
        return self.output_dir
    
    def export_basic_data(self, data, timestamp):
        """导出基础数据"""
        print("📊 导出基础数据...")
        
        basic_data = {
            'export_time': datetime.now().isoformat(),
            'primary_colors': self._convert_colors_to_list(data.get('primary_colors', [])),
            'secondary_colors': self._convert_colors_to_list(data.get('secondary_colors', [])),
            'all_colors': self._convert_colors_to_list(data.get('all_colors', [])),
            'top_colors': self._convert_colors_to_list(data.get('top_colors', [])),
            'data_summary': {
                'primary_colors_count': len(data.get('primary_colors', [])),
                'secondary_colors_count': len(data.get('secondary_colors', [])),
                'total_schemes': len(data.get('all_schemes', [])),
                'gradient_schemes': len(data.get('gradient_schemes', [])),
                'insertion_schemes': len(data.get('insertion_schemes', []))
            }
        }
        
        # JSON格式
        json_path = self.json_dir / f"basic_data_{timestamp}.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(basic_data, f, indent=2, ensure_ascii=False)
        
        # CSV格式 - 颜色数据
        if 'primary_colors' in data:
            self._export_colors_to_csv(data['primary_colors'], 
                                     self.csv_dir / f"primary_colors_{timestamp}.csv",
                                     "Primary Colors")
        
        if 'secondary_colors' in data:
            self._export_colors_to_csv(data['secondary_colors'], 
                                     self.csv_dir / f"secondary_colors_{timestamp}.csv",
                                     "Secondary Colors")
    
    def export_color_schemes(self, data, timestamp):
        """导出颜色方案数据"""
        print("🎨 导出颜色方案数据...")
        
        # 导出所有方案
        if 'all_schemes' in data:
            schemes_data = []
            for i, scheme in enumerate(data['all_schemes']):
                scheme_info = {
                    'scheme_id': i + 1,
                    'type': scheme.get('type', 'unknown'),
                    'colors': self._convert_colors_to_list(scheme.get('colors', [])),
                    'color_count': len(scheme.get('colors', [])),
                    'overall_score': scheme.get('overall_score', 0),
                    'harmony_score': scheme.get('harmony_score', 0),
                    'contrast_score': scheme.get('contrast_score', 0),
                    'saturation_score': scheme.get('saturation_score', 0),
                    'brightness_score': scheme.get('brightness_score', 0)
                }
                schemes_data.append(scheme_info)
            
            # JSON格式
            json_path = self.json_dir / f"all_schemes_{timestamp}.json"
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(schemes_data, f, indent=2, ensure_ascii=False)
            
            # Excel格式
            self._export_schemes_to_excel(schemes_data, 
                                        self.excel_dir / f"all_schemes_{timestamp}.xlsx")
        
        # 分别导出渐变方案和插入方案
        for scheme_type in ['gradient_schemes', 'insertion_schemes']:
            if scheme_type in data:
                self._export_specific_schemes(data[scheme_type], scheme_type, timestamp)
    
    def export_evaluation_results(self, data, timestamp):
        """导出评估结果"""
        print("📈 导出评估结果...")
        
        if 'evaluated_schemes' in data:
            eval_data = []
            for scheme in data['evaluated_schemes']:
                eval_info = {
                    'scheme_id': scheme.get('id', 'unknown'),
                    'overall_score': scheme.get('overall_score', 0),
                    'harmony_score': scheme.get('harmony_score', 0),
                    'contrast_score': scheme.get('contrast_score', 0),
                    'saturation_score': scheme.get('saturation_score', 0),
                    'brightness_score': scheme.get('brightness_score', 0),
                    'complexity_score': scheme.get('complexity_score', 0),
                    'quality_grade': self._get_quality_grade(scheme.get('overall_score', 0)),
                    'colors': self._convert_colors_to_list(scheme.get('colors', []))
                }
                eval_data.append(eval_info)
            
            # JSON格式
            json_path = self.json_dir / f"evaluation_results_{timestamp}.json"
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(eval_data, f, indent=2, ensure_ascii=False)
            
            # CSV格式
            csv_path = self.csv_dir / f"evaluation_results_{timestamp}.csv"
            df = pd.DataFrame(eval_data)
            # 移除colors列用于CSV导出（太复杂）
            df_csv = df.drop('colors', axis=1)
            df_csv.to_csv(csv_path, index=False, encoding='utf-8-sig')
    
    def export_correlation_analysis(self, data, timestamp):
        """导出相关度分析数据"""
        print("🔗 导出相关度分析数据...")
        
        if 'correlation_matrix' in data and data['correlation_matrix'] is not None:
            correlation_matrix = data['correlation_matrix']
            
            # 安全检查相关度矩阵
            try:
                if isinstance(correlation_matrix, np.ndarray):
                    if correlation_matrix.size == 0:
                        print("⚠️ 相关度矩阵为空，跳过导出")
                        return
                elif isinstance(correlation_matrix, list):
                    if len(correlation_matrix) == 0:
                        print("⚠️ 相关度矩阵为空，跳过导出")
                        return
                else:
                    print("⚠️ 相关度矩阵格式不支持，跳过导出")
                    return
            except Exception as e:
                print(f"⚠️ 检查相关度矩阵时出错: {e}，跳过导出")
                return
            
            # 转换为DataFrame便于导出
            df = pd.DataFrame(correlation_matrix)
            
            # Excel格式
            excel_path = self.excel_dir / f"correlation_matrix_{timestamp}.xlsx"
            df.to_excel(excel_path, index=True)
            
            # CSV格式
            csv_path = self.csv_dir / f"correlation_matrix_{timestamp}.csv"
            df.to_csv(csv_path, index=True, encoding='utf-8-sig')
            
            # JSON格式（包含统计信息）
            correlation_data = {
                'matrix': correlation_matrix.tolist() if hasattr(correlation_matrix, 'tolist') else correlation_matrix,
                'statistics': {
                    'mean_correlation': float(np.mean(correlation_matrix)),
                    'max_correlation': float(np.max(correlation_matrix)),
                    'min_correlation': float(np.min(correlation_matrix)),
                    'std_correlation': float(np.std(correlation_matrix))
                }
            }
            
            json_path = self.json_dir / f"correlation_analysis_{timestamp}.json"
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(correlation_data, f, indent=2, ensure_ascii=False)
    
    def export_statistics(self, data, timestamp):
        """导出统计数据"""
        print("📊 导出统计数据...")
        
        stats = {
            'export_time': datetime.now().isoformat(),
            'data_overview': {
                'total_schemes': len(data.get('all_schemes', [])),
                'gradient_schemes': len(data.get('gradient_schemes', [])),
                'insertion_schemes': len(data.get('insertion_schemes', [])),
                'evaluated_schemes': len(data.get('evaluated_schemes', [])),
                'primary_colors': len(data.get('primary_colors', [])),
                'secondary_colors': len(data.get('secondary_colors', []))
            }
        }
        
        # 计算评分统计
        if 'evaluated_schemes' in data and data['evaluated_schemes']:
            scores = [s.get('overall_score', 0) for s in data['evaluated_schemes']]
            harmony_scores = [s.get('harmony_score', 0) for s in data['evaluated_schemes']]
            contrast_scores = [s.get('contrast_score', 0) for s in data['evaluated_schemes']]
            
            stats['score_statistics'] = {
                'overall_scores': {
                    'mean': float(np.mean(scores)),
                    'std': float(np.std(scores)),
                    'min': float(np.min(scores)),
                    'max': float(np.max(scores)),
                    'median': float(np.median(scores))
                },
                'harmony_scores': {
                    'mean': float(np.mean(harmony_scores)),
                    'std': float(np.std(harmony_scores)),
                    'min': float(np.min(harmony_scores)),
                    'max': float(np.max(harmony_scores))
                },
                'contrast_scores': {
                    'mean': float(np.mean(contrast_scores)),
                    'std': float(np.std(contrast_scores)),
                    'min': float(np.min(contrast_scores)),
                    'max': float(np.max(contrast_scores))
                }
            }
            
            # 质量等级分布
            quality_grades = [self._get_quality_grade(score) for score in scores]
            grade_counts = {grade: quality_grades.count(grade) 
                          for grade in ['Excellent', 'Good', 'Fair', 'Poor']}
            stats['quality_distribution'] = grade_counts
        
        # JSON格式
        json_path = self.json_dir / f"statistics_{timestamp}.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
    
    def generate_color_palettes(self, data, timestamp):
        """生成颜色调色板图像"""
        print("🎨 生成颜色调色板图像...")
        
        # 生成主要颜色调色板
        if 'primary_colors' in data:
            self._create_palette_image(data['primary_colors'], 
                                     self.palettes_dir / f"primary_palette_{timestamp}.png",
                                     "Primary Colors Palette")
        
        # 生成次要颜色调色板
        if 'secondary_colors' in data:
            self._create_palette_image(data['secondary_colors'], 
                                     self.palettes_dir / f"secondary_palette_{timestamp}.png",
                                     "Secondary Colors Palette")
        
        # 生成顶级颜色调色板
        if 'top_colors' in data:
            self._create_palette_image(data['top_colors'], 
                                     self.palettes_dir / f"top_colors_palette_{timestamp}.png",
                                     "Top Colors Palette")
        
        # 生成最佳方案调色板
        if 'best_schemes' in data:
            for i, scheme in enumerate(data['best_schemes'][:5]):  # 只生成前5个
                if 'colors' in scheme:
                    self._create_palette_image(scheme['colors'], 
                                             self.palettes_dir / f"best_scheme_{i+1}_{timestamp}.png",
                                             f"Best Scheme {i+1}")
    
    def generate_export_report(self, data, timestamp):
        """生成导出报告"""
        print("📋 生成导出报告...")
        
        report_path = self.output_dir / f"export_report_{timestamp}.txt"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("颜色方案数据导出报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("数据概览:\n")
            f.write("-" * 20 + "\n")
            f.write(f"总方案数: {len(data.get('all_schemes', []))}\n")
            f.write(f"渐变方案数: {len(data.get('gradient_schemes', []))}\n")
            f.write(f"插入方案数: {len(data.get('insertion_schemes', []))}\n")
            f.write(f"主要颜色数: {len(data.get('primary_colors', []))}\n")
            f.write(f"次要颜色数: {len(data.get('secondary_colors', []))}\n\n")
            
            f.write("导出文件列表:\n")
            f.write("-" * 20 + "\n")
            
            # 列出所有导出的文件
            for subdir in [self.json_dir, self.csv_dir, self.excel_dir, self.palettes_dir]:
                f.write(f"\n{subdir.name.upper()} 文件:\n")
                for file_path in subdir.glob(f"*{timestamp}*"):
                    f.write(f"  - {file_path.name}\n")
            
            f.write("\n使用说明:\n")
            f.write("-" * 20 + "\n")
            f.write("1. JSON文件: 包含完整的数据结构，适合程序读取\n")
            f.write("2. CSV文件: 适合在Excel中打开和分析\n")
            f.write("3. Excel文件: 包含格式化的数据表格\n")
            f.write("4. 调色板图像: 可视化的颜色展示\n")
    
    def _convert_colors_to_list(self, colors):
        """将颜色数组转换为列表格式"""
        if isinstance(colors, np.ndarray):
            return colors.tolist()
        elif isinstance(colors, list):
            return [[int(c) for c in color] if isinstance(color, (list, np.ndarray)) else color 
                   for color in colors]
        else:
            return []
    
    def _export_colors_to_csv(self, colors, file_path, title):
        """导出颜色到CSV文件"""
        with open(file_path, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            writer.writerow(['Color_ID', 'R', 'G', 'B', 'Hex'])
            
            for i, color in enumerate(colors):
                if isinstance(color, (list, np.ndarray)) and len(color) >= 3:
                    r, g, b = int(color[0]), int(color[1]), int(color[2])
                    hex_color = f"#{r:02x}{g:02x}{b:02x}"
                    writer.writerow([i+1, r, g, b, hex_color])
    
    def _export_schemes_to_excel(self, schemes_data, file_path):
        """导出方案到Excel文件"""
        # 创建主要信息的DataFrame
        main_data = []
        for scheme in schemes_data:
            main_info = {k: v for k, v in scheme.items() if k != 'colors'}
            main_data.append(main_info)
        
        df_main = pd.DataFrame(main_data)
        
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            df_main.to_excel(writer, sheet_name='Schemes_Summary', index=False)
            
            # 为每个方案创建详细的颜色信息表（只导出前10个方案）
            for i, scheme in enumerate(schemes_data[:10]):
                if 'colors' in scheme and scheme['colors']:
                    colors_data = []
                    for j, color in enumerate(scheme['colors']):
                        if isinstance(color, list) and len(color) >= 3:
                            r, g, b = int(color[0]), int(color[1]), int(color[2])
                            colors_data.append({
                                'Color_Index': j+1,
                                'R': r,
                                'G': g,
                                'B': b,
                                'Hex': f"#{r:02x}{g:02x}{b:02x}"
                            })
                    
                    if colors_data:
                        df_colors = pd.DataFrame(colors_data)
                        sheet_name = f'Scheme_{i+1}_Colors'
                        df_colors.to_excel(writer, sheet_name=sheet_name, index=False)
    
    def _export_specific_schemes(self, schemes, scheme_type, timestamp):
        """导出特定类型的方案"""
        schemes_data = []
        for i, scheme in enumerate(schemes):
            scheme_info = {
                'scheme_id': scheme.get('scheme_id', i+1),
                'colors': self._convert_colors_to_list(scheme.get('colors', [])),
                'type': scheme_type.replace('_schemes', '')
            }
            
            # 添加特定字段
            if scheme_type == 'gradient_schemes':
                scheme_info.update({
                    'num_steps': scheme.get('num_steps', 0),
                    'noise_strength': scheme.get('noise_strength', 0),
                    'quality_score': scheme.get('quality_score', 0)
                })
            
            schemes_data.append(scheme_info)
        
        # JSON格式
        json_path = self.json_dir / f"{scheme_type}_{timestamp}.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(schemes_data, f, indent=2, ensure_ascii=False)
    
    def _get_quality_grade(self, score):
        """根据分数获取质量等级"""
        if score >= 0.8:
            return 'Excellent'
        elif score >= 0.6:
            return 'Good'
        elif score >= 0.4:
            return 'Fair'
        else:
            return 'Poor'
    
    def _create_palette_image(self, colors, file_path, title, width=800, height=200):
        """创建调色板图像"""
        # 安全检查颜色数据
        if colors is None:
            return
        
        # 处理NumPy数组和列表的长度检查
        try:
            if isinstance(colors, np.ndarray):
                if colors.size == 0:
                    return
            elif isinstance(colors, list):
                if len(colors) == 0:
                    return
            else:
                return
        except Exception:
            return
        
        img = Image.new('RGB', (width, height + 50), 'white')
        draw = ImageDraw.Draw(img)
        
        # 绘制标题
        try:
            # 尝试使用系统字体
            from PIL import ImageFont
            font = ImageFont.truetype("arial.ttf", 20)
        except:
            font = None
        
        draw.text((10, 10), title, fill='black', font=font)
        
        # 绘制颜色块
        color_width = width // len(colors)
        
        for i, color in enumerate(colors):
            if isinstance(color, (list, np.ndarray)) and len(color) >= 3:
                r, g, b = int(color[0]), int(color[1]), int(color[2])
                x1 = i * color_width
                x2 = (i + 1) * color_width
                
                draw.rectangle([(x1, 40), (x2, height + 40)], fill=(r, g, b))
                
                # 添加RGB值标签
                text = f"RGB({r},{g},{b})"
                text_bbox = draw.textbbox((0, 0), text, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_x = x1 + (color_width - text_width) // 2
                
                # 根据颜色亮度选择文字颜色
                brightness = (r * 0.299 + g * 0.587 + b * 0.114)
                text_color = 'white' if brightness < 128 else 'black'
                
                draw.text((text_x, height - 30), text, fill=text_color, font=font)
        
        img.save(file_path)


def export_color_scheme_data(data, output_dir="./exports"):
    """便捷函数：导出颜色方案数据"""
    exporter = ColorSchemeDataExporter(output_dir)
    return exporter.export_all_data(data)


if __name__ == "__main__":
    # 测试导出功能
    print("颜色方案数据导出模块测试")
    
    # 创建测试数据
    test_data = {
        'primary_colors': np.random.randint(0, 256, (8, 3)),
        'secondary_colors': np.random.randint(0, 256, (4, 3)),
        'all_schemes': [
            {
                'colors': np.random.randint(0, 256, (5, 3)),
                'overall_score': np.random.uniform(0.3, 0.9),
                'harmony_score': np.random.uniform(0.2, 0.8),
                'contrast_score': np.random.uniform(0.3, 0.9)
            }
            for _ in range(10)
        ]
    }
    
    # 执行导出
    export_color_scheme_data(test_data)
    print("测试完成！")