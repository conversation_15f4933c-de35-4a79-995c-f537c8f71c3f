#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图像预处理模块
用于消除光照、环境和阴影效果，提高颜色聚类准确性
"""

import os
import sys
import numpy as np
import cv2
from PIL import Image, ImageEnhance, ImageFilter
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import matplotlib
from matplotlib.font_manager import FontProperties

# 设置中文字体支持
try:
    matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    matplotlib.rcParams['axes.unicode_minus'] = False
except:
    pass

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from src.utils import ensure_dir

class ImagePreprocessor:
    """图像预处理器"""
    
    def __init__(self, config):
        self.config = config
        self.preprocessing_config = config.get('preprocessing', {})
        
        # 预处理参数
        self.shadow_removal_enabled = self.preprocessing_config.get('shadow_removal', True)
        self.lighting_normalization_enabled = self.preprocessing_config.get('lighting_normalization', True)
        self.color_enhancement_enabled = self.preprocessing_config.get('color_enhancement', True)
        self.histogram_equalization_enabled = self.preprocessing_config.get('histogram_equalization', True)
        
        # 阴影检测参数
        self.shadow_threshold = self.preprocessing_config.get('shadow_threshold', 0.7)
        self.shadow_dilation_kernel = self.preprocessing_config.get('shadow_dilation_kernel', 5)
        
        # 光照归一化参数
        self.gamma_correction = self.preprocessing_config.get('gamma_correction', 1.2)
        self.brightness_factor = self.preprocessing_config.get('brightness_factor', 1.1)
        self.contrast_factor = self.preprocessing_config.get('contrast_factor', 1.1)
        
    def preprocess_image(self, image_path, output_path=None):
        """
        预处理单张图像
        
        Args:
            image_path: 输入图像路径
            output_path: 输出图像路径（可选）
            
        Returns:
            preprocessed_image: 预处理后的图像（PIL Image对象）
        """
        try:
            # 读取图像
            image = Image.open(image_path).convert('RGB')
            original_image = image.copy()
            
            print(f"正在预处理图像: {os.path.basename(image_path)}")
            
            # 1. 阴影去除
            if self.shadow_removal_enabled:
                image = self._remove_shadows(image)
                print("  ✓ 阴影去除完成")
            
            # 2. 光照归一化
            if self.lighting_normalization_enabled:
                image = self._normalize_lighting(image)
                print("  ✓ 光照归一化完成")
            
            # 3. 颜色增强
            if self.color_enhancement_enabled:
                image = self._enhance_colors(image)
                print("  ✓ 颜色增强完成")
            
            # 4. 直方图均衡化
            if self.histogram_equalization_enabled:
                image = self._histogram_equalization(image)
                print("  ✓ 直方图均衡化完成")
            
            # 保存预处理后的图像
            if output_path:
                ensure_dir(os.path.dirname(output_path))
                image.save(output_path, quality=95)
                print(f"  ✓ 预处理图像已保存: {output_path}")
            
            return image
            
        except Exception as e:
            print(f"预处理图像 {image_path} 时出错: {str(e)}")
            return None
    
    def _remove_shadows(self, image):
        """去除阴影"""
        try:
            # 转换为numpy数组
            img_array = np.array(image)
            
            # 转换为LAB颜色空间
            img_lab = cv2.cvtColor(img_array, cv2.COLOR_RGB2LAB)
            
            # 分离L通道（亮度）
            l_channel = img_lab[:, :, 0]
            
            # 使用形态学操作检测阴影
            # 创建结构元素
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, 
                                             (self.shadow_dilation_kernel, self.shadow_dilation_kernel))
            
            # 形态学开运算去除小的亮区
            opened = cv2.morphologyEx(l_channel, cv2.MORPH_OPEN, kernel)
            
            # 形态学闭运算填充阴影区域
            closed = cv2.morphologyEx(opened, cv2.MORPH_CLOSE, kernel)
            
            # 计算阴影掩码
            shadow_mask = l_channel < (closed * self.shadow_threshold)
            
            # 对阴影区域进行亮度补偿
            compensated_l = l_channel.copy().astype(np.float32)
            compensated_l[shadow_mask] = compensated_l[shadow_mask] * (1.0 / self.shadow_threshold)
            compensated_l = np.clip(compensated_l, 0, 255).astype(np.uint8)
            
            # 重新组合LAB图像
            img_lab[:, :, 0] = compensated_l
            
            # 转换回RGB
            img_rgb = cv2.cvtColor(img_lab, cv2.COLOR_LAB2RGB)
            
            return Image.fromarray(img_rgb)
            
        except Exception as e:
            print(f"阴影去除失败: {str(e)}")
            return image
    
    def _normalize_lighting(self, image):
        """光照归一化"""
        try:
            # Gamma校正
            img_array = np.array(image).astype(np.float32) / 255.0
            gamma_corrected = np.power(img_array, 1.0 / self.gamma_correction)
            gamma_corrected = (gamma_corrected * 255).astype(np.uint8)
            
            # 转换回PIL图像
            normalized_image = Image.fromarray(gamma_corrected)
            
            # 亮度调整
            enhancer = ImageEnhance.Brightness(normalized_image)
            normalized_image = enhancer.enhance(self.brightness_factor)
            
            # 对比度调整
            enhancer = ImageEnhance.Contrast(normalized_image)
            normalized_image = enhancer.enhance(self.contrast_factor)
            
            return normalized_image
            
        except Exception as e:
            print(f"光照归一化失败: {str(e)}")
            return image
    
    def _enhance_colors(self, image):
        """颜色增强"""
        try:
            # 饱和度增强
            enhancer = ImageEnhance.Color(image)
            enhanced_image = enhancer.enhance(1.2)  # 增强20%的饱和度
            
            # 锐化处理
            enhanced_image = enhanced_image.filter(ImageFilter.UnsharpMask(radius=1, percent=150, threshold=3))
            
            return enhanced_image
            
        except Exception as e:
            print(f"颜色增强失败: {str(e)}")
            return image
    
    def _histogram_equalization(self, image):
        """直方图均衡化"""
        try:
            # 转换为numpy数组
            img_array = np.array(image)
            
            # 转换为YUV颜色空间
            img_yuv = cv2.cvtColor(img_array, cv2.COLOR_RGB2YUV)
            
            # 对Y通道进行直方图均衡化
            img_yuv[:, :, 0] = cv2.equalizeHist(img_yuv[:, :, 0])
            
            # 转换回RGB
            img_rgb = cv2.cvtColor(img_yuv, cv2.COLOR_YUV2RGB)
            
            return Image.fromarray(img_rgb)
            
        except Exception as e:
            print(f"直方图均衡化失败: {str(e)}")
            return image
    
    def preprocess_image_list(self, image_paths, output_dir=None):
        """
        批量预处理图像列表
        
        Args:
            image_paths: 图像路径列表
            output_dir: 输出目录（可选）
            
        Returns:
            preprocessed_paths: 预处理后的图像路径列表
        """
        preprocessed_paths = []
        
        print(f"开始批量预处理 {len(image_paths)} 张图像...")
        
        for i, image_path in enumerate(image_paths):
            try:
                # 生成输出路径
                if output_dir:
                    filename = os.path.basename(image_path)
                    name, ext = os.path.splitext(filename)
                    output_path = os.path.join(output_dir, f"{name}_preprocessed{ext}")
                else:
                    output_path = None
                
                # 预处理图像
                preprocessed_image = self.preprocess_image(image_path, output_path)
                
                if preprocessed_image is not None:
                    if output_path:
                        preprocessed_paths.append(output_path)
                    else:
                        preprocessed_paths.append(image_path)  # 如果没有保存，返回原路径
                
                # 显示进度
                progress = (i + 1) / len(image_paths) * 100
                print(f"预处理进度: {progress:.1f}% ({i+1}/{len(image_paths)})")
                
            except Exception as e:
                print(f"预处理图像 {image_path} 时出错: {str(e)}")
        
        print(f"批量预处理完成，成功处理 {len(preprocessed_paths)} 张图像")
        return preprocessed_paths
    
    def create_comparison_visualization(self, original_path, preprocessed_path, output_path):
        """创建预处理前后对比可视化"""
        try:
            # 读取图像
            original = Image.open(original_path)
            preprocessed = Image.open(preprocessed_path)
            
            # 创建对比图
            fig, axes = plt.subplots(1, 2, figsize=(12, 6))
            
            axes[0].imshow(original)
            axes[0].set_title('原始图像', fontsize=12, fontweight='bold')
            axes[0].axis('off')
            
            axes[1].imshow(preprocessed)
            axes[1].set_title('预处理后', fontsize=12, fontweight='bold')
            axes[1].axis('off')
            
            plt.tight_layout()
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"对比可视化已保存: {output_path}")
            
        except Exception as e:
            print(f"创建对比可视化失败: {str(e)}")
