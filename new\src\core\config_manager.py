﻿#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
配置管理器模块
负责系统配置的加载、验证和管理

作者: 建筑色彩优化团队
版本: 3.0.0
"""

import json
import os
import logging
from typing import Dict, Any, Optional
from pathlib import Path


class ConfigManager:
    """
    配置管理器类
    
    功能：
    1. 加载JSON配置文件
    2. 验证配置参数的有效性
    3. 提供配置参数的访问接口
    4. 支持配置的动态更新
    """
    
    def __init__(self, config_path: str = "config/settings.json"):
        """
        初始化配置管理器
        
        参数:
            config_path (str): 配置文件路径，默认为 config/settings.json
        """
        self.config_path = Path(config_path)
        self.config_data = {}
        self.logger = logging.getLogger(__name__)
        
        # 加载配置文件
        self._load_config()
        
        # 验证配置
        if not self._validate_config():
            raise ValueError("配置文件验证失败，请检查配置参数")
    
    def _load_config(self) -> None:
        """
        从JSON文件加载配置
        
        异常处理：
        - FileNotFoundError: 配置文件不存在
        - json.JSONDecodeError: JSON格式错误
        - Exception: 其他加载错误
        """
        try:
            # 检查配置文件是否存在
            if not self.config_path.exists():
                raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
            
            # 读取并解析JSON配置文件
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config_data = json.load(f)
            
            self.logger.info(f"配置文件加载成功: {self.config_path}")
            
        except FileNotFoundError as e:
            self.logger.error(f"配置文件不存在: {e}")
            raise
        except json.JSONDecodeError as e:
            self.logger.error(f"配置文件JSON格式错误: {e}")
            raise
        except Exception as e:
            self.logger.error(f"配置文件加载失败: {e}")
            raise
    
    def _validate_config(self) -> bool:
        """
        验证配置文件的完整性和有效性
        
        返回:
            bool: 验证成功返回True，失败返回False
        """
        try:
            # 检查必需的顶级配置节
            required_sections = [
                'system', 'data', 'preprocessing', 'segmentation',
                'clustering', 'correlation', 'color_generation',
                'evaluation', 'visualization', 'logging', 'performance'
            ]
            
            for section in required_sections:
                if section not in self.config_data:
                    self.logger.error(f"缺少必需的配置节: {section}")
                    return False
            
            # 验证数据路径配置
            data_config = self.config_data['data']
            required_data_keys = ['input_dir', 'output_dir', 'supported_formats']
            for key in required_data_keys:
                if key not in data_config:
                    self.logger.error(f"数据配置缺少必需参数: {key}")
                    return False
            
            # 验证聚类配置
            clustering_config = self.config_data['clustering']
            if clustering_config.get('n_colors_primary', 0) <= 0:
                self.logger.error("主要颜色数量必须大于0")
                return False
            
            # 验证评估权重配置
            eval_weights = self.config_data['evaluation']['weights']
            weight_sum = sum(eval_weights.values())
            if abs(weight_sum - 1.0) > 0.01:  # 允许小的浮点误差
                self.logger.warning(f"评估权重总和不等于1.0: {weight_sum}")
            
            self.logger.info("配置文件验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证过程中出错: {e}")
            return False
    
    def get_config(self, section: Optional[str] = None) -> Dict[str, Any]:
        """
        获取配置参数
        
        参数:
            section (str, optional): 配置节名称，如果为None则返回全部配置
        
        返回:
            Dict[str, Any]: 配置字典
        """
        if section is None:
            return self.config_data.copy()
        
        if section not in self.config_data:
            self.logger.warning(f"配置节不存在: {section}")
            return {}
        
        return self.config_data[section].copy()
    
    def get_value(self, key_path: str, default: Any = None) -> Any:
        """
        通过点分隔的路径获取配置值
        
        参数:
            key_path (str): 配置路径，如 'data.input_dir'
            default (Any): 默认值
        
        返回:
            Any: 配置值
        
        示例:
            >>> config.get_value('data.input_dir')
            'data/input/street_view'
            >>> config.get_value('clustering.n_colors_primary')
            12
        """
        try:
            keys = key_path.split('.')
            value = self.config_data
            
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default
            
            return value
            
        except Exception as e:
            self.logger.warning(f"获取配置值失败 {key_path}: {e}")
            return default
    
    def update_config(self, section: str, updates: Dict[str, Any]) -> None:
        """
        更新配置参数
        
        参数:
            section (str): 配置节名称
            updates (Dict[str, Any]): 要更新的配置项
        """
        try:
            if section not in self.config_data:
                self.logger.warning(f"配置节不存在，将创建新节: {section}")
                self.config_data[section] = {}
            
            # 更新配置
            self.config_data[section].update(updates)
            
            # 重新验证配置
            if self._validate_config():
                self.logger.info(f"配置节 {section} 更新成功")
            else:
                self.logger.error("配置更新后验证失败")
            
        except Exception as e:
            self.logger.error(f"更新配置失败: {e}")
            raise
    
    def save_config(self, output_path: Optional[str] = None) -> None:
        """
        保存配置到文件
        
        参数:
            output_path (str, optional): 输出路径，如果为None则覆盖原文件
        """
        try:
            save_path = Path(output_path) if output_path else self.config_path
            
            # 确保目录存在
            save_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存配置文件
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"配置文件保存成功: {save_path}")
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            raise
    
    def get_data_paths(self) -> Dict[str, str]:
        """
        获取所有数据路径配置
        
        返回:
            Dict[str, str]: 数据路径字典
        """
        data_config = self.get_config('data')
        return {
            'input_dir': data_config.get('input_dir', 'data/input/street_view'),
            'reference_colors_dir': data_config.get('reference_colors_dir', 'data/input/reference_colors'),
            'output_dir': data_config.get('output_dir', 'data/output'),
            'preprocessed_dir': os.path.join(data_config.get('output_dir', 'data/output'), 'preprocessed'),
            'segmentation_dir': os.path.join(data_config.get('output_dir', 'data/output'), 'segmentation'),
            'clustering_dir': os.path.join(data_config.get('output_dir', 'data/output'), 'clustering'),
            'correlation_dir': os.path.join(data_config.get('output_dir', 'data/output'), 'correlation'),
            'color_schemes_dir': os.path.join(data_config.get('output_dir', 'data/output'), 'color_schemes'),
            'evaluation_dir': os.path.join(data_config.get('output_dir', 'data/output'), 'evaluation'),
            'visualization_dir': os.path.join(data_config.get('output_dir', 'data/output'), 'visualization'),
            'exports_dir': os.path.join(data_config.get('output_dir', 'data/output'), 'exports')
        }
    
    def __str__(self) -> str:
        """返回配置管理器的字符串表示"""
        return f"ConfigManager(config_path={self.config_path}, sections={list(self.config_data.keys())})"
    
    def __repr__(self) -> str:
        """返回配置管理器的详细表示"""
        return self.__str__()


# 全局配置管理器实例
# 在其他模块中可以直接导入使用: from src.core.config_manager import config_manager
config_manager = None

def get_config_manager(config_path: str = "config/settings.json") -> ConfigManager:
    """
    获取全局配置管理器实例（单例模式）
    
    参数:
        config_path (str): 配置文件路径
    
    返回:
        ConfigManager: 配置管理器实例
    """
    global config_manager
    if config_manager is None:
        config_manager = ConfigManager(config_path)
    return config_manager


if __name__ == "__main__":
    # 测试代码
    try:
        # 创建配置管理器
        config = ConfigManager("config/settings.json")
        
        # 测试获取配置
        print("系统配置:", config.get_config('system'))
        print("数据路径:", config.get_data_paths())
        print("聚类颜色数:", config.get_value('clustering.n_colors_primary'))
        
        # 测试配置更新
        config.update_config('test', {'new_param': 'test_value'})
        print("更新后的配置节:", config.get_config('test'))
        
    except Exception as e:
        print(f"测试失败: {e}")
