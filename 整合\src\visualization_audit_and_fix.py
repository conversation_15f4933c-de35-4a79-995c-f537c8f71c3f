#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化审计和修复脚本
检查所有可视化图表的数据来源，拒绝模拟数据，优化图面效果
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.data_loader import load_color_scheme_data
from src.visualization.enhanced_sci_charts import EnhancedSCICharts
from src.utils import ensure_dir

class VisualizationAuditor:
    """可视化审计器"""
    
    def __init__(self):
        self.issues = []
        self.fixes = []
        
    def audit_data_sources(self, data):
        """审计数据来源"""
        print("🔍 审计数据来源...")
        
        # 检查是否使用真实数据
        real_data_checks = [
            ('all_schemes', '所有方案数据'),
            ('evaluated_schemes', '评估方案数据'),
            ('correlation_matrix', '相关度矩阵'),
            ('all_colors', '颜色数据'),
            ('gradient_schemes', '渐变方案'),
            ('insertion_schemes', '插入方案')
        ]
        
        for key, description in real_data_checks:
            if key in data:
                if isinstance(data[key], list) and len(data[key]) > 0:
                    print(f"✅ {description}: {len(data[key])} 项真实数据")
                elif isinstance(data[key], np.ndarray):
                    print(f"✅ {description}: {data[key].shape} 真实数据")
                else:
                    self.issues.append(f"❌ {description}: 数据格式异常")
            else:
                self.issues.append(f"❌ 缺少{description}")
        
        # 检查颜色数据完整性
        if 'evaluated_schemes' in data:
            schemes_with_colors = 0
            for scheme in data['evaluated_schemes'][:10]:
                if 'colors' in scheme and scheme['colors'] is not None:
                    schemes_with_colors += 1
            
            if schemes_with_colors >= 5:
                print(f"✅ 方案颜色数据: {schemes_with_colors}/10 个方案有颜色数据")
            else:
                self.issues.append(f"❌ 方案颜色数据不足: 仅{schemes_with_colors}/10个方案有颜色数据")
        
        return len(self.issues) == 0
    
    def check_simulation_usage(self, chart_generator):
        """检查是否使用模拟数据"""
        print("🔍 检查模拟数据使用情况...")
        
        # 检查enhanced_sci_charts.py中的模拟数据使用
        chart_file = Path(__file__).parent / "visualization" / "enhanced_sci_charts.py"
        
        if chart_file.exists():
            with open(chart_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 查找模拟数据关键词
            simulation_keywords = [
                'np.random',
                'random.uniform',
                'random.randint',
                '模拟数据',
                'simulation',
                'mock_data'
            ]
            
            found_simulations = []
            for keyword in simulation_keywords:
                if keyword in content:
                    found_simulations.append(keyword)
            
            if found_simulations:
                self.issues.append(f"❌ 发现模拟数据使用: {', '.join(found_simulations)}")
            else:
                print("✅ 未发现模拟数据使用")
        
        return len(found_simulations) == 0

def create_comprehensive_visualization_system():
    """创建全面的可视化系统"""
    print("🚀 创建全面的可视化系统")
    print("=" * 60)
    
    # 1. 加载真实数据
    print("1. 加载真实数据...")
    data = load_color_scheme_data()
    
    if not data:
        print("❌ 数据加载失败")
        return False
    
    # 2. 审计数据来源
    auditor = VisualizationAuditor()
    data_ok = auditor.audit_data_sources(data)
    
    if not data_ok:
        print("❌ 数据审计失败:")
        for issue in auditor.issues:
            print(f"  {issue}")
        return False
    
    # 3. 创建优化的图表生成器
    print("\n2. 创建优化的图表生成器...")
    config = {}
    output_dir = "./outputs/visualization_optimized"
    ensure_dir(output_dir)
    
    chart_generator = EnhancedSCICharts(config, output_dir)
    chart_generator.set_data(data)
    
    # 4. 生成所有图表（拒绝模拟数据）
    print("\n3. 生成优化的可视化图表...")
    
    chart_methods = [
        ("01_clustering_results_sci.png", "聚类结果可视化", 
         chart_generator.generate_clustering_results_visualization),
        ("02_correlation_heatmap_sci.png", "颜色相关度热力图", 
         chart_generator.generate_color_correlation_heatmap),
        ("03_network_graph_sci.png", "颜色网络关系图", 
         chart_generator.generate_color_network_graph),
        ("04_gradient_process_sci.png", "渐变生成过程图", 
         chart_generator.generate_gradient_process_chart),
        ("05_scheme_statistics_sci.png", "方案生成统计图", 
         chart_generator.generate_scheme_generation_statistics),
        ("06_evaluation_radar_sci.png", "多维度评估雷达图", 
         chart_generator.generate_multidimensional_evaluation_radar),
        ("07_optimal_comparison_sci.png", "最优方案对比分析图", 
         chart_generator.generate_optimal_schemes_comparison),
        ("08_all_schemes_analysis_sci.png", "所有方案总体分析图", 
         chart_generator.generate_all_schemes_analysis)
    ]
    
    successful_charts = 0
    failed_charts = 0
    
    for filename, description, method in chart_methods:
        print(f"\n🔄 生成 {description} ({filename})")
        try:
            result = method()
            if result and os.path.exists(result):
                file_size = os.path.getsize(result)
                print(f"✅ {description} 生成成功")
                print(f"   文件: {result}")
                print(f"   大小: {file_size:,} bytes")
                successful_charts += 1
            else:
                print(f"❌ {description} 生成失败")
                failed_charts += 1
        except Exception as e:
            print(f"❌ {description} 生成出错: {e}")
            failed_charts += 1
    
    # 5. 总结结果
    print("\n" + "=" * 60)
    print("可视化系统优化结果")
    print("=" * 60)
    print(f"✅ 成功生成: {successful_charts} 个图表")
    print(f"❌ 生成失败: {failed_charts} 个图表")
    print(f"📊 成功率: {successful_charts/(successful_charts+failed_charts)*100:.1f}%")
    
    if auditor.issues:
        print(f"\n⚠️  发现 {len(auditor.issues)} 个问题:")
        for issue in auditor.issues:
            print(f"  {issue}")
    
    return successful_charts > 0

if __name__ == "__main__":
    success = create_comprehensive_visualization_system()
    if success:
        print("\n🎉 可视化系统优化完成!")
    else:
        print("\n❌ 可视化系统优化失败!")
