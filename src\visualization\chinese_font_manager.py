#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
中文字体管理系统
为建筑立面色彩优化系统提供完善的中文字体支持
"""

import os
import sys
import platform
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from pathlib import Path
import logging

class ChineseFontManager:
    """中文字体管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.system_type = platform.system()
        self.available_fonts = []
        self.selected_font = None
        
        # 定义中文字体优先级列表
        self.font_priority = {
            'Windows': [
                'Microsoft YaHei',
                'SimHei',
                'SimSun',
                'KaiTi',
                'FangSong',
                'Microsoft JhengHei'
            ],
            'Darwin': [  # macOS
                'PingFang SC',
                'Heiti SC',
                'STHeiti',
                'Arial Unicode MS',
                'Hiragino Sans GB'
            ],
            'Linux': [
                'Noto Sans CJK SC',
                'WenQuanYi Micro Hei',
                'WenQuanYi Zen Hei',
                'Droid Sans Fallback',
                'AR PL UMing CN'
            ]
        }
        
        # 备用字体
        self.fallback_fonts = ['DejaVu Sans', 'Arial', 'sans-serif']
        
        # 初始化字体系统
        self._initialize_font_system()
    
    def _initialize_font_system(self):
        """初始化字体系统"""
        try:
            self.logger.info("正在初始化中文字体系统...")
            
            # 检测可用字体
            self._detect_available_fonts()
            
            # 选择最佳字体
            self._select_best_font()
            
            # 配置matplotlib
            self._configure_matplotlib()
            
            self.logger.info(f"中文字体系统初始化完成，选择字体: {self.selected_font}")
            
        except Exception as e:
            self.logger.error(f"字体系统初始化失败: {str(e)}")
            self._use_fallback_font()
    
    def _detect_available_fonts(self):
        """检测系统中可用的中文字体"""
        try:
            # 获取系统字体优先级列表
            priority_fonts = self.font_priority.get(self.system_type, [])
            
            # 获取所有系统字体
            all_fonts = [f.name for f in fm.fontManager.ttflist]
            
            # 检查优先级字体是否可用
            for font_name in priority_fonts:
                if self._is_font_available(font_name, all_fonts):
                    self.available_fonts.append(font_name)
                    self.logger.info(f"发现可用中文字体: {font_name}")
            
            # 如果没有找到优先级字体，搜索其他中文字体
            if not self.available_fonts:
                self._search_chinese_fonts(all_fonts)
            
            self.logger.info(f"共检测到 {len(self.available_fonts)} 个可用中文字体")
            
        except Exception as e:
            self.logger.error(f"字体检测失败: {str(e)}")
            self.available_fonts = []
    
    def _is_font_available(self, font_name, all_fonts):
        """检查指定字体是否可用"""
        # 精确匹配
        if font_name in all_fonts:
            return True
        
        # 模糊匹配
        for font in all_fonts:
            if font_name.lower() in font.lower() or font.lower() in font_name.lower():
                return True
        
        return False
    
    def _search_chinese_fonts(self, all_fonts):
        """搜索其他可能的中文字体"""
        chinese_keywords = [
            'hei', 'kai', 'song', 'ming', 'yuan', 'fang',
            'chinese', 'cjk', 'han', 'noto', 'source',
            'wenquanyi', 'droid', 'arphic'
        ]
        
        for font in all_fonts:
            font_lower = font.lower()
            if any(keyword in font_lower for keyword in chinese_keywords):
                if self._test_font_chinese_support(font):
                    self.available_fonts.append(font)
                    self.logger.info(f"发现其他中文字体: {font}")
    
    def _test_font_chinese_support(self, font_name):
        """测试字体是否支持中文字符"""
        try:
            # 创建临时图形测试字体
            fig, ax = plt.subplots(figsize=(1, 1))
            ax.text(0.5, 0.5, '测试中文', fontname=font_name, fontsize=12)
            plt.close(fig)
            return True
        except Exception:
            return False
    
    def _select_best_font(self):
        """选择最佳字体"""
        if self.available_fonts:
            # 选择优先级最高的可用字体
            priority_fonts = self.font_priority.get(self.system_type, [])
            
            for priority_font in priority_fonts:
                for available_font in self.available_fonts:
                    if priority_font.lower() in available_font.lower():
                        self.selected_font = available_font
                        self.logger.info(f"选择优先级字体: {available_font}")
                        return
            
            # 如果没有匹配的优先级字体，选择第一个可用字体
            self.selected_font = self.available_fonts[0]
            self.logger.info(f"选择第一个可用字体: {self.selected_font}")
        else:
            # 没有找到中文字体，使用备用字体
            self.selected_font = self.fallback_fonts[0]
            self.logger.warning("未找到中文字体，使用备用字体")
    
    def force_use_chinese_font(self):
        """强制使用中文字体"""
        if self.available_fonts:
            # 直接使用第一个可用的中文字体
            chinese_font = self.available_fonts[0]
            plt.rcParams['font.sans-serif'] = [chinese_font] + self.fallback_fonts
            plt.rcParams['axes.unicode_minus'] = False
            self.selected_font = chinese_font
            self.logger.info(f"强制使用中文字体: {chinese_font}")
            return True
        return False
    
    def _configure_matplotlib(self):
        """配置matplotlib使用中文字体"""
        try:
            # 设置字体列表
            font_list = [self.selected_font] + self.fallback_fonts
            
            # 配置matplotlib参数
            plt.rcParams['font.sans-serif'] = font_list
            plt.rcParams['axes.unicode_minus'] = False
            plt.rcParams['font.family'] = 'sans-serif'
            
            # 尝试清除字体缓存（兼容不同matplotlib版本）
            try:
                if hasattr(fm, '_rebuild'):
                    fm._rebuild()
                elif hasattr(fm.fontManager, 'findfont'):
                    # 强制重新加载字体管理器
                    fm.fontManager.__init__()
            except Exception as cache_error:
                self.logger.warning(f"字体缓存清理失败: {cache_error}")
            
            self.logger.info(f"matplotlib字体配置完成: {font_list}")
            
        except Exception as e:
            self.logger.error(f"matplotlib字体配置失败: {str(e)}")
            self._use_fallback_font()
    
    def _use_fallback_font(self):
        """使用备用字体"""
        try:
            plt.rcParams['font.sans-serif'] = self.fallback_fonts
            plt.rcParams['axes.unicode_minus'] = False
            self.selected_font = self.fallback_fonts[0]
            self.logger.info(f"使用备用字体: {self.selected_font}")
        except Exception as e:
            self.logger.error(f"备用字体配置也失败: {str(e)}")
    
    def get_font_name(self):
        """获取当前选择的字体名称"""
        return self.selected_font
    
    def get_available_fonts(self):
        """获取所有可用的中文字体列表"""
        return self.available_fonts.copy()
    
    def test_chinese_rendering(self):
        """测试中文字符渲染"""
        try:
            fig, ax = plt.subplots(figsize=(8, 6))
            
            test_texts = [
                "建筑立面色彩优化系统",
                "聚类结果可视化图",
                "颜色相关度热力图",
                "最优方案对比分析图",
                "ABCDEFG 123456789"
            ]
            
            for i, text in enumerate(test_texts):
                ax.text(0.1, 0.8 - i * 0.15, text, fontsize=14, 
                       transform=ax.transAxes)
            
            ax.set_title(f"中文字体渲染测试 - {self.selected_font}", fontsize=16)
            ax.axis('off')
            
            # 保存测试图片
            test_path = "font_test.png"
            fig.savefig(test_path, dpi=150, bbox_inches='tight')
            plt.close(fig)
            
            self.logger.info(f"中文字体渲染测试完成，测试图片保存为: {test_path}")
            return test_path
            
        except Exception as e:
            self.logger.error(f"中文字体渲染测试失败: {str(e)}")
            return None
    
    def get_font_info(self):
        """获取字体信息"""
        return {
            'system_type': self.system_type,
            'selected_font': self.selected_font,
            'available_fonts': self.available_fonts,
            'fallback_fonts': self.fallback_fonts,
            'font_count': len(self.available_fonts)
        }
    
    def print_font_info(self):
        """打印字体信息"""
        info = self.get_font_info()
        
        print("\n" + "="*60)
        print("中文字体管理系统信息")
        print("="*60)
        print(f"操作系统: {info['system_type']}")
        print(f"选择字体: {info['selected_font']}")
        print(f"可用字体数量: {info['font_count']}")
        
        if info['available_fonts']:
            print("\n可用中文字体:")
            for i, font in enumerate(info['available_fonts'], 1):
                print(f"  {i}. {font}")
        
        print(f"\n备用字体: {', '.join(info['fallback_fonts'])}")
        print("="*60)

def main():
    """主函数 - 测试字体管理系统"""
    # 设置日志
    logging.basicConfig(level=logging.INFO, 
                       format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 创建字体管理器
    font_manager = ChineseFontManager()
    
    # 打印字体信息
    font_manager.print_font_info()
    
    # 测试中文渲染
    test_result = font_manager.test_chinese_rendering()
    if test_result:
        print(f"\n✅ 中文字体渲染测试成功！测试图片: {test_result}")
    else:
        print("\n❌ 中文字体渲染测试失败！")

if __name__ == "__main__":
    main()