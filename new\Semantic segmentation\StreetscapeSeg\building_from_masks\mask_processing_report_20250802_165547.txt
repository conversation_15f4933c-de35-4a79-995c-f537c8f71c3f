色块图建筑提取报告
==================================================

处理时间: 2025-08-02 16:55:47
色块图目录: D:\桌面\颜色相关度\整合\Semantic segmentation\StreetscapeSeg\out
原始图目录: D:\桌面\颜色相关度\整合\Semantic segmentation\StreetscapeSeg\pic
输出目录: D:\桌面\颜色相关度\整合\Semantic segmentation\StreetscapeSeg\building_from_masks

处理统计:
- 总文件对数: 112
- 成功提取: 0
- 失败数量: 112
- 成功率: 0.0%

建筑识别颜色:
- building: RGB(120, 120, 120)
- wall: RGB(180, 120, 120)
- house: RGB(6, 230, 230)
- skyscraper: RGB(80, 50, 50)
- fence: RGB(4, 200, 3)

输出文件类型:
1. 白色背景版本: 非建筑部分为白色
2. 透明背景版本: 非建筑部分透明（PNG格式）
3. 轮廓版本: 只显示建筑轮廓，其他为黑色
4. 颜色分析: 每张图的颜色分布JSON文件

颜色匹配参数:
- 颜色容差: 10
