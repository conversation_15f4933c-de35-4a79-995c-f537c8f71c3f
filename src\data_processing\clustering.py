import os
import numpy as np
from sklearn.cluster import KMeans
import pandas as pd
from PIL import Image
import sys
import json

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from src.utils import get_colors_from_image, create_color_block, create_color_palette, ensure_dir

class BaseClusterer:
    """基础聚类器"""
    
    def __init__(self, config):
        self.config = config
        self.n_clusters = 12  # 默认聚类数
        self.filter_threshold = config['clustering']['filter_threshold']
        self.output_dir = None
        
    def get_colors_from_folder(self, folder_path):
        """从文件夹中提取所有图像的颜色"""
        all_colors = []
        
        # 获取文件夹中的所有图像文件
        image_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) 
                      if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
        
        if not image_files:
            print(f"警告: 文件夹 {folder_path} 中没有找到图像文件")
            return np.array([])
        
        for img_path in image_files:
            try:
                colors = get_colors_from_image(img_path, self.filter_threshold)
                if colors is not None and len(colors) > 0:
                    all_colors.extend(colors)
            except Exception as e:
                print(f"处理图像 {img_path} 时出错: {str(e)}")
        
        return np.array(all_colors)
    
    def cluster_colors(self, colors, n_clusters=None):
        """对颜色进行聚类"""
        if n_clusters is None:
            n_clusters = self.n_clusters
            
        if len(colors) == 0:
            print("警告: 没有颜色数据可供聚类")
            return np.array([]), np.array([])
        
        # 使用KMeans进行聚类
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        kmeans.fit(colors)
        
        # 获取聚类中心和标签
        cluster_centers = kmeans.cluster_centers_.astype(int)
        labels = kmeans.labels_
        
        # 计算每个聚类的比例
        unique_labels, counts = np.unique(labels, return_counts=True)
        proportions = counts / len(labels)
        
        return cluster_centers, proportions
    
    def save_results(self, cluster_centers, proportions, output_dir, prefix="cluster"):
        """保存聚类结果"""
        ensure_dir(output_dir)
        
        # 保存聚类中心颜色
        np.save(os.path.join(output_dir, f"{prefix}_centers.npy"), cluster_centers)
        
        # 保存聚类比例
        np.save(os.path.join(output_dir, f"{prefix}_proportions.npy"), proportions)
        
        # 保存为CSV文件
        df = pd.DataFrame({
            'R': cluster_centers[:, 0],
            'G': cluster_centers[:, 1],
            'B': cluster_centers[:, 2],
            'Proportion': proportions
        })
        df.to_csv(os.path.join(output_dir, f"{prefix}_results.csv"), index=False)
        
        # 生成颜色块图像
        for i, color in enumerate(cluster_centers):
            color_path = os.path.join(output_dir, f"{prefix}_color_{i+1}.png")
            create_color_block(color, color_path)
        
        # 生成颜色比例图
        palette_path = os.path.join(output_dir, f"{prefix}_palette.png")
        create_color_palette(cluster_centers, proportions, palette_path, 
                            title=f"{prefix.capitalize()} Colors")
        
        # 保存为文本文件
        with open(os.path.join(output_dir, f"{prefix}_results.txt"), 'w') as f:
            f.write(f"聚类结果 ({len(cluster_centers)} 种颜色):\n")
            f.write("=" * 40 + "\n\n")
            
            for i, (color, prop) in enumerate(zip(cluster_centers, proportions)):
                f.write(f"颜色 {i+1}: RGB{tuple(map(int, color))} - 比例: {prop:.2%}\n")
        
        return os.path.join(output_dir, f"{prefix}_results.txt")


class PrimaryClusterer(BaseClusterer):
    """一层数据聚类器"""

    def __init__(self, config):
        super().__init__(config)
        self.n_clusters = config['clustering']['primary_clusters']
        self.input_dir = config['data']['primary_dir']
        self.output_dir = config['output']['clustering_dir']

    def process(self, image_files=None):
        """处理一层数据

        Args:
            image_files: 可选的图像文件列表，如果提供则使用这些文件，否则从input_dir读取
        """
        print("开始处理一层数据...")

        # 如果提供了图像文件列表，使用这些文件；否则从文件夹读取
        if image_files is not None:
            all_colors = []
            print(f"处理 {len(image_files)} 张图像...")
            for i, img_path in enumerate(image_files):
                try:
                    colors = get_colors_from_image(img_path)
                    if colors is not None and len(colors) > 0:
                        all_colors.extend(colors)
                    print(f"进度: {i+1}/{len(image_files)}", end='\r')
                except Exception as e:
                    print(f"处理图像 {img_path} 时出错: {str(e)}")
            print()  # 换行
        else:
            # 从一层数据文件夹中提取颜色
            all_colors = self.get_colors_from_folder(self.input_dir)

        if len(all_colors) == 0:
            print("错误: 一层数据中没有有效的颜色数据")
            return None

        # 聚类
        cluster_centers, proportions = self.cluster_colors(all_colors, self.n_clusters)

        # 保存结果
        output_path = os.path.join(self.output_dir, "primary")
        ensure_dir(output_path)
        self.save_results(cluster_centers, proportions, output_path, "primary")

        print(f"一层数据处理完成，聚类中心: {len(cluster_centers)} 个")

        # 返回聚类中心颜色（不返回比例，保持与调用代码一致）
        return cluster_centers


class SecondaryClusterer(BaseClusterer):
    """二层数据聚类器"""

    def __init__(self, config):
        super().__init__(config)
        # 修改为固定4个聚类
        self.n_clusters = 4  # 固定为4个聚类
        self.input_dir = config['data']['secondary_dir']
        self.output_dir = config['output']['clustering_dir']

    def process(self, image_files=None):
        """处理二层数据

        Args:
            image_files: 可选的图像文件列表，如果提供则使用这些文件，否则从input_dir读取
        """
        print("开始处理二层数据...")

        # 如果提供了图像文件列表，使用这些文件；否则从文件夹读取
        if image_files is not None:
            all_colors = []
            print(f"处理 {len(image_files)} 张图像...")
            for i, img_path in enumerate(image_files):
                try:
                    colors = get_colors_from_image(img_path)
                    if colors is not None and len(colors) > 0:
                        all_colors.extend(colors)
                    print(f"进度: {i+1}/{len(image_files)}", end='\r')
                except Exception as e:
                    print(f"处理图像 {img_path} 时出错: {str(e)}")
            print()  # 换行
        else:
            # 从二层数据文件夹中提取颜色
            all_colors = self.get_colors_from_folder(self.input_dir)

        if len(all_colors) == 0:
            print("错误: 二层数据中没有有效的颜色数据")
            return None

        # 聚类
        cluster_centers, proportions = self.cluster_colors(all_colors, self.n_clusters)

        # 保存结果
        output_path = os.path.join(self.output_dir, "secondary")
        ensure_dir(output_path)
        self.save_results(cluster_centers, proportions, output_path, "secondary")

        print(f"二层数据处理完成，聚类中心: {len(cluster_centers)} 个")
        print(f"二层数据聚类为4个颜色")

        # 返回聚类中心颜色（不返回比例，保持与调用代码一致）
        return cluster_centers