import os
import numpy as np
import pandas as pd
import json
import sys
import colorsys
from PIL import Image
from sklearn.cluster import KMeans

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from src.utils import ensure_dir, create_color_palette

class ColorEvaluator:
    """颜色评估器类"""
    
    def __init__(self, config):
        self.config = config
        self.harmony_weight = config['evaluation']['harmony_weight']
        self.color_difference_weight = config['evaluation']['color_difference_weight']
        self.historical_harmony_weight = config['evaluation']['historical_harmony_weight']
        self.best_schemes_count = config['evaluation']['best_schemes_count']
        self.historical_colors = np.array(config['data']['historical_colors'])
        self.output_dir = config['output']['evaluation_dir']
        ensure_dir(self.output_dir)
        
        # 初始化评估结果存储
        self.evaluation_results = []
        self.best_schemes = []
    
    def calculate_color_harmony(self, colors, proportions):
        """计算颜色和谐度
        
        参数:
            colors: 颜色列表
            proportions: 比例列表
            
        返回:
            和谐度得分（0-1，越高越和谐）
        """
        # 将RGB颜色转换为HSV
        hsv_colors = []
        for color in colors:
            r, g, b = color[0]/255.0, color[1]/255.0, color[2]/255.0
            hsv_colors.append(colorsys.rgb_to_hsv(r, g, b))
        hsv_colors = np.array(hsv_colors)
        
        # 计算色相、饱和度和明度的标准差
        h_std = np.std(hsv_colors[:, 0])
        s_std = np.std(hsv_colors[:, 1])
        v_std = np.std(hsv_colors[:, 2])
        
        # 和谐度得分（标准差越小越和谐）
        harmony_score = 1 - (h_std + s_std + v_std) / 3
        
        return harmony_score
    
    def calculate_color_difference(self, colors):
        """计算与历史色彩的色相差值
        
        参数:
            colors: 颜色列表
            
        返回:
            色相差值（越小越好）
        """
        min_difference = float('inf')
        for hist_color in self.historical_colors:
            for color in colors:
                # 计算RGB空间的欧氏距离
                diff = np.sqrt(np.sum((hist_color - color) ** 2))
                min_difference = min(min_difference, diff)
        
        # 归一化差值（0-1，越小越好）
        normalized_diff = min_difference / 441.67  # 最大可能差值（[0,0,0]与[255,255,255]的距离）
        
        return normalized_diff
    
    def calculate_adjacent_harmony(self, colors, proportions):
        """计算相邻颜色和谐度
        
        参数:
            colors: 颜色列表
            proportions: 比例列表
            
        返回:
            相邻和谐度得分（0-1，越高越和谐）
        """
        # 计算相邻颜色对的和谐度
        harmony_scores = []
        for i in range(len(colors) - 1):
            color1 = colors[i]
            color2 = colors[i + 1]
            
            # 计算RGB空间的欧氏距离
            diff = np.sqrt(np.sum((color1 - color2) ** 2))
            
            # 归一化差值（0-1，越小越不和谐）
            normalized_diff = diff / 441.67
            
            # 和谐度（1 - 差值，越高越和谐）
            harmony = 1 - normalized_diff
            
            # 考虑颜色比例的权重
            weight = (proportions[i] + proportions[i + 1]) / 2
            harmony_scores.append(harmony * weight)
        
        # 计算加权平均和谐度
        if harmony_scores:
            adjacent_harmony = np.mean(harmony_scores)
        else:
            adjacent_harmony = 1.0  # 默认值
        
        return adjacent_harmony
    
    def evaluate_scheme(self, scheme):
        """评估单个方案
        
        参数:
            scheme: 方案字典
            
        返回:
            评估结果字典
        """
        colors = np.array(scheme['colors'])
        proportions = np.array(scheme['color_proportions'])
        
        # 计算三个维度的得分
        harmony_score = self.calculate_color_harmony(colors, proportions)
        color_difference = self.calculate_color_difference(colors)
        adjacent_harmony = self.calculate_adjacent_harmony(colors, proportions)
        
        # 计算综合得分（和谐度和相邻和谐度越高越好，色相差值越小越好）
        composite_score = (
            self.harmony_weight * harmony_score + 
            self.color_difference_weight * (1 - color_difference) + 
            self.historical_harmony_weight * adjacent_harmony
        )
        
        return {
            'scheme_id': scheme['scheme_id'],
            'harmony_score': harmony_score,
            'color_difference': color_difference,
            'adjacent_harmony': adjacent_harmony,
            'composite_score': composite_score,
            'image_path': scheme['image_path'],
            'info_path': scheme['info_path'],
            'palette_path': scheme['palette_path']
        }
    
    def get_user_input(self, prompt, default=None, input_type=str):
        """获取用户输入，带有默认值"""
        if default is not None:
            prompt = f"{prompt} [默认: {default}]: "
        else:
            prompt = f"{prompt}: "
        
        user_input = input(prompt).strip()
        
        if user_input == "" and default is not None:
            return default
        
        if input_type == int:
            try:
                return int(user_input)
            except ValueError:
                print("请输入有效的整数！")
                return self.get_user_input(prompt, default, input_type)
        elif input_type == float:
            try:
                return float(user_input)
            except ValueError:
                print("请输入有效的数字！")
                return self.get_user_input(prompt, default, input_type)
        elif input_type == bool:
            return user_input.lower() in ('yes', 'y', 'true', 't', '1')
        else:
            return user_input
    
    def process(self, insertion_schemes):
        """处理评估
        
        参数:
            insertion_schemes: 插入方案列表
            
        返回:
            最佳方案列表
        """
        print("开始评估方案...")
        
        # 询问是否使用配置文件中的评估权重
        use_config_weights = self.get_user_input("是否使用配置文件中的评估权重? (y/n)", "y")
        if use_config_weights.lower() not in ('yes', 'y', 'true', 't', '1'):
            self.harmony_weight = self.get_user_input("请输入和谐度权重", self.harmony_weight, float)
            self.color_difference_weight = self.get_user_input("请输入色相差值权重", self.color_difference_weight, float)
            self.historical_harmony_weight = self.get_user_input("请输入历史和谐度权重", self.historical_harmony_weight, float)
            
            # 确保权重总和为1
            total_weight = self.harmony_weight + self.color_difference_weight + self.historical_harmony_weight
            if abs(total_weight - 1.0) > 0.001:
                print(f"警告: 权重总和 ({total_weight}) 不为1，将进行归一化")
                self.harmony_weight /= total_weight
                self.color_difference_weight /= total_weight
                self.historical_harmony_weight /= total_weight
                print(f"归一化后的权重: 和谐度={self.harmony_weight:.2f}, 色相差值={self.color_difference_weight:.2f}, 历史和谐度={self.historical_harmony_weight:.2f}")
        
        # 询问是否修改最佳方案数量
        change_best_count = self.get_user_input("是否修改最佳方案数量? (y/n)", "n")
        if change_best_count.lower() in ('yes', 'y', 'true', 't', '1'):
            self.best_schemes_count = self.get_user_input("请输入最佳方案数量", self.best_schemes_count, int)
        
        # 评估所有方案
        all_results = []
        for scheme in insertion_schemes:
            result = self.evaluate_scheme(scheme)
            all_results.append(result)
        
        # 保存评估结果到实例变量
        self.evaluation_results = all_results
        
        # 保存所有方案的评估结果到Excel表格
        df = pd.DataFrame(all_results)
        df = df[['scheme_id', 'harmony_score', 'color_difference', 'adjacent_harmony', 'composite_score']]
        table_path = os.path.join(self.output_dir, 'all_schemes_evaluation.xlsx')
        df.to_excel(table_path, index=False)
        
        # 根据综合得分排序
        sorted_results = sorted(all_results, key=lambda x: x['composite_score'], reverse=True)
        
        # 选择前N个最佳方案
        best_schemes = sorted_results[:self.best_schemes_count]
        
        # 显示最佳方案评分
        print("\n最佳方案评分:")
        for i, scheme in enumerate(best_schemes):
            print(f"\n方案 {i+1} (原方案ID: {scheme['scheme_id']}):")
            print(f"和谐度得分: {scheme['harmony_score']:.4f}")
            print(f"色相差值: {scheme['color_difference']:.4f}")
            print(f"相邻和谐度: {scheme['adjacent_harmony']:.4f}")
            print(f"综合评分: {scheme['composite_score']:.4f}")
        
        # 询问是否接受这些最佳方案
        accept_best = self.get_user_input("\n是否接受这些最佳方案? (y/n)", "y")
        if accept_best.lower() not in ('yes', 'y', 'true', 't', '1'):
            print("\n请选择您想要的方案ID (从所有方案中选择):")
            
            # 显示所有方案的ID和评分
            for i, result in enumerate(sorted_results[:20]):  # 只显示前20个
                print(f"{i+1}. 方案ID: {result['scheme_id']}, 综合评分: {result['composite_score']:.4f}")
            
            # 让用户选择方案
            selected_schemes = []
            for i in range(self.best_schemes_count):
                while True:
                    try:
                        idx = self.get_user_input(f"请选择第 {i+1} 个方案 (输入序号)", i+1, int) - 1
                        if 0 <= idx < len(sorted_results):
                            selected_schemes.append(sorted_results[idx])
                            break
                        else:
                            print("请输入有效的序号！")
                    except ValueError:
                        print("请输入有效的序号！")
            
            best_schemes = selected_schemes
        
        # 保存最佳方案
        for i, scheme in enumerate(best_schemes):
            src_path = scheme['image_path']
            dst_path = os.path.join(self.output_dir, f'best_scheme_{i+1}.png')
            
            # 复制图像
            Image.open(src_path).save(dst_path)
            
            # 复制调色板
            src_palette = scheme['palette_path']
            dst_palette = os.path.join(self.output_dir, f'best_scheme_palette_{i+1}.png')
            Image.open(src_palette).save(dst_palette)
        
        # 保存最佳方案评估结果
        with open(os.path.join(self.output_dir, 'best_schemes_evaluation.txt'), 'w') as f:
            f.write("最佳方案评估结果：\n")
            f.write("=" * 40 + "\n\n")
            
            for i, scheme in enumerate(best_schemes):
                f.write(f"\n方案 {i+1} (原方案ID: {scheme['scheme_id']}):\n")
                f.write(f"和谐度得分: {scheme['harmony_score']:.4f}\n")
                f.write(f"色相差值: {scheme['color_difference']:.4f}\n")
                f.write(f"相邻和谐度: {scheme['adjacent_harmony']:.4f}\n")
                f.write(f"综合评分: {scheme['composite_score']:.4f}\n")
        
        # 保存最佳方案到实例变量
        self.best_schemes = best_schemes
        
        # 生成评分表格
        self.generate_score_table(best_schemes)
        
        # 询问是否生成评分可视化
        generate_viz = self.get_user_input("是否生成评分可视化? (y/n)", "y")
        if generate_viz.lower() in ('yes', 'y', 'true', 't', '1'):
            self.generate_score_visualization(best_schemes)
        
        print(f"方案评估完成，最佳 {self.best_schemes_count} 个方案保存在: {self.output_dir}")
        
        return best_schemes
    
    def generate_score_table(self, schemes):
        """生成评分表格"""
        table_path = os.path.join(self.output_dir, 'best_schemes_score_table.xlsx')
        data = []
        for i, scheme in enumerate(schemes):
            data.append([
                f'方案 {i+1}', 
                scheme['harmony_score'], 
                scheme['color_difference'],
                scheme['adjacent_harmony'], 
                scheme['composite_score']
            ])
        
        df = pd.DataFrame(
            data, 
            columns=['方案', '和谐度得分', '色相差值', '相邻和谐度', '综合评分']
        )
        df.to_excel(table_path, index=False)
    
    def generate_score_visualization(self, schemes):
        """生成评分可视化"""
        import matplotlib.pyplot as plt
        
        # 准备数据
        scheme_ids = [f'方案 {i+1}' for i in range(len(schemes))]
        harmony_scores = [s['harmony_score'] for s in schemes]
        color_differences = [1 - s['color_difference'] for s in schemes]  # 转换为正向指标
        adjacent_harmonies = [s['adjacent_harmony'] for s in schemes]
        composite_scores = [s['composite_score'] for s in schemes]
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # 设置条形宽度
        bar_width = 0.2
        
        # 设置位置
        r1 = np.arange(len(scheme_ids))
        r2 = [x + bar_width for x in r1]
        r3 = [x + bar_width for x in r2]
        r4 = [x + bar_width for x in r3]
        
        # 创建条形图
        ax.bar(r1, harmony_scores, width=bar_width, label='和谐度得分', color='skyblue')
        ax.bar(r2, color_differences, width=bar_width, label='色相相似度', color='lightgreen')
        ax.bar(r3, adjacent_harmonies, width=bar_width, label='相邻和谐度', color='salmon')
        ax.bar(r4, composite_scores, width=bar_width, label='综合评分', color='purple')
        
        # 添加标签和图例
        ax.set_xlabel('方案')
        ax.set_ylabel('得分')
        ax.set_title('最佳方案评分对比')
        ax.set_xticks([r + bar_width*1.5 for r in range(len(scheme_ids))])
        ax.set_xticklabels(scheme_ids)
        ax.legend()
        
        # 保存图表
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'best_schemes_scores.png'), dpi=300)
        plt.close()

    def evaluate_schemes(self, all_schemes):
        """
        评估色彩方案 - process方法的别名

        Args:
            all_schemes: 所有色彩方案列表（渐变方案 + 插入方案）

        Returns:
            dict: 包含评估结果的字典
        """
        # 由于process方法期望的是插入方案，我们需要处理混合的方案列表
        # 分离渐变方案和插入方案
        gradient_schemes = []
        insertion_schemes = []

        for scheme in all_schemes:
            # 根据方案的特征判断类型
            if 'gradient_id' in scheme or 'num_steps' in scheme:
                gradient_schemes.append(scheme)
            else:
                insertion_schemes.append(scheme)

        # 如果有插入方案，优先评估插入方案
        if insertion_schemes:
            best_schemes = self.process(insertion_schemes)
        elif gradient_schemes:
            # 如果只有渐变方案，也进行评估
            best_schemes = self.process(gradient_schemes)
        else:
            # 如果方案列表为空或无法识别类型，尝试直接处理
            best_schemes = self.process(all_schemes)

        # 返回标准化的结果格式
        return {
            'best_schemes': best_schemes,
            'total_evaluated': len(all_schemes),
            'best_count': len(best_schemes),
            'evaluation_results_path': os.path.join(self.output_dir, "evaluation_results.json"),
            'score_table_path': os.path.join(self.output_dir, "best_schemes_score_table.xlsx"),
            'score_visualization_path': os.path.join(self.output_dir, "best_schemes_scores.png")
        }