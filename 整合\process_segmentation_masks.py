#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
色块图处理器 - 从分割色块图中快速提取建筑
利用已有的语义分割结果，快速生成建筑提取图

功能：
1. 读取分割产生的色块图
2. 识别建筑相关的颜色
3. 快速生成建筑提取结果
4. 支持批量处理
5. 生成多种输出格式

使用方法：
python process_segmentation_masks.py
"""

import os
import sys
from pathlib import Path
import numpy as np

# 尝试导入cv2，如果失败则提示用户
try:
    import cv2  # type: ignore
except ImportError:
    print("错误: 无法导入cv2 (OpenCV)")
    print("请确保已安装opencv-python包:")
    print("pip install opencv-python")
    sys.exit(1)

from PIL import Image
import pandas as pd
from datetime import datetime



class SegmentationMaskProcessor:
    """分割色块图处理器"""
    
    def __init__(self, mask_dir, original_dir, output_dir=None):
        """
        初始化处理器
        
        参数:
            mask_dir (str): 分割色块图目录
            original_dir (str): 原始图片目录
            output_dir (str): 输出目录
        """
        self.mask_dir = Path(mask_dir)
        self.original_dir = Path(original_dir)
        
        if output_dir is None:
            self.output_dir = self.mask_dir.parent / "building_from_masks"
        else:
            self.output_dir = Path(output_dir)
        
        # 创建输出子目录
        self.white_bg_dir = self.output_dir / "white_background"
        self.transparent_dir = self.output_dir / "transparent_background"
        self.contour_dir = self.output_dir / "contour_only"
        self.mask_analysis_dir = self.output_dir / "mask_analysis"
        
        for dir_path in [self.white_bg_dir, self.transparent_dir, self.contour_dir, self.mask_analysis_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # ADE20K数据集的建筑相关颜色（RGB值）
        # 这些是ADE20K标准调色板中建筑相关类别的颜色
        self.building_colors = {
            'building': (120, 120, 120),    # 建筑 - 灰色
            'wall': (180, 120, 120),        # 墙 - 浅红灰色
            'house': (6, 230, 230),         # 房屋 - 青色
            'skyscraper': (80, 50, 50),     # 摩天大楼 - 深红棕色
            'fence': (4, 200, 3),           # 围栏 - 绿色（有时也算建筑结构）
        }
        
        # 颜色容差（用于颜色匹配）
        self.color_tolerance = 10
        
        print(f"🚀 色块图处理器初始化完成")
        print(f"📁 色块图目录: {self.mask_dir}")
        print(f"📁 原始图目录: {self.original_dir}")
        print(f"📁 输出目录: {self.output_dir}")

    def is_valid_image_file(self, file_path):
        """
        检查是否是有效的图片文件
        只检查文件扩展名，不过滤任何文件名

        参数:
            file_path (Path): 文件路径

        返回:
            bool: 是否是有效的图片文件
        """
        filename = file_path.name.lower()

        # 只检查文件扩展名是否有效
        valid_extensions = ['.png', '.jpg', '.jpeg']
        return any(filename.endswith(ext) for ext in valid_extensions)
    
    def is_valid_segmentation_file(self, file_path):
        """
        检查是否是有效的分割文件
        过滤掉拼接图片等不需要处理的文件

        参数:
            file_path (Path): 文件路径

        返回:
            bool: 是否是有效的分割文件
        """
        # 首先检查是否是有效的图片文件
        if not self.is_valid_image_file(file_path):
            return False
        
        # 过滤掉拼接图片
        filename = file_path.name.lower()
        if filename.startswith('stitched_'):
            return False
            
        return True
    
    def analyze_mask_colors(self, mask_path):
        """
        分析色块图中的颜色分布

        参数:
            mask_path (Path): 色块图路径

        返回:
            dict: 颜色分析结果
        """
        try:
            # 使用PIL读取色块图（避免中文路径问题）
            from PIL import Image
            mask_img = Image.open(mask_path)
            if mask_img.mode != 'RGB':
                mask_img = mask_img.convert('RGB')

            # 转换为numpy数组
            mask_rgb = np.array(mask_img)
            
            # 获取唯一颜色及其像素数量
            pixels = mask_rgb.reshape(-1, 3)
            unique_colors, counts = np.unique(pixels, axis=0, return_counts=True)
            
            # 计算总像素数
            total_pixels = mask_rgb.shape[0] * mask_rgb.shape[1]
            
            # 分析结果（确保所有数据类型都是JSON可序列化的）
            color_analysis = {
                'total_pixels': int(total_pixels),  # 转换为Python int
                'unique_colors': int(len(unique_colors)),  # 转换为Python int
                'color_distribution': []
            }
            
            # 按像素数量排序
            sorted_indices = np.argsort(counts)[::-1]
            
            for idx in sorted_indices:
                color = unique_colors[idx]
                count = counts[idx]
                percentage = (count / total_pixels) * 100

                color_analysis['color_distribution'].append({
                    'color_rgb': [int(color[0]), int(color[1]), int(color[2])],  # 转换为Python int
                    'pixel_count': int(count),  # 转换为Python int
                    'percentage': float(percentage)  # 转换为Python float
                })
            
            return color_analysis
            
        except Exception as e:
            print(f"   ❌ 颜色分析失败: {e}")
            return None
    
    def create_building_mask_from_colors(self, mask_path):
        """
        根据颜色创建建筑mask

        参数:
            mask_path (Path): 色块图路径

        返回:
            numpy.ndarray: 建筑mask，失败返回None
        """
        try:
            # 使用PIL读取色块图（避免中文路径问题）
            from PIL import Image
            mask_img = Image.open(mask_path)
            if mask_img.mode != 'RGB':
                mask_img = mask_img.convert('RGB')

            # 转换为numpy数组
            mask_rgb = np.array(mask_img)
            
            # 创建建筑mask
            building_mask = np.zeros(mask_rgb.shape[:2], dtype=bool)
            
            # 检查每个建筑相关颜色
            for class_name, target_color in self.building_colors.items():
                # 计算颜色距离
                color_diff = np.sqrt(np.sum((mask_rgb - target_color) ** 2, axis=2))
                
                # 在容差范围内的像素认为是该类别
                class_mask = color_diff <= self.color_tolerance
                
                # 合并到建筑mask
                building_mask |= class_mask
                
                # 统计该类别的像素数
                class_pixels = np.sum(class_mask)
                if class_pixels > 0:
                    print(f"   找到 {class_name}: {class_pixels} 像素")
            
            return building_mask
            
        except Exception as e:
            print(f"   ❌ 建筑mask创建失败: {e}")
            return None
    
    def extract_building_from_mask(self, mask_path, original_path):
        """
        从色块图中提取建筑部分
        
        参数:
            mask_path (Path): 色块图路径
            original_path (Path): 原始图片路径
            
        返回:
            bool: 是否成功
        """
        try:
            print(f"   处理: {mask_path.name}")

            # 检查文件是否存在
            if not mask_path.exists():
                print(f"   ❌ 色块图文件不存在: {mask_path}")
                return False

            if not original_path.exists():
                print(f"   ❌ 原始图文件不存在: {original_path}")
                return False

            # 分析色块图颜色
            color_analysis = self.analyze_mask_colors(mask_path)
            if color_analysis is None:
                return False
            
            # 保存颜色分析结果为文本文件（避免JSON序列化问题）
            analysis_file = self.mask_analysis_dir / f"{mask_path.stem}_color_analysis.txt"
            with open(analysis_file, 'w', encoding='utf-8') as f:
                f.write(f"颜色分析结果 - {mask_path.name}\n")
                f.write("="*50 + "\n")
                f.write(f"总像素数: {color_analysis['total_pixels']}\n")
                f.write(f"唯一颜色数: {color_analysis['unique_colors']}\n")
                f.write("\n颜色分布:\n")
                for i, color_info in enumerate(color_analysis['color_distribution'][:10]):  # 只保存前10个
                    f.write(f"{i+1}. RGB{color_info['color_rgb']} - {color_info['pixel_count']}像素 ({color_info['percentage']:.2f}%)\n")
            
            # 创建建筑mask
            building_mask = self.create_building_mask_from_colors(mask_path)
            if building_mask is None:
                return False
            
            # 检查是否找到建筑
            building_pixels = np.sum(building_mask)
            if building_pixels == 0:
                print(f"   ⚠️  未找到建筑颜色: {mask_path.name}")
                return False
            
            total_pixels = building_mask.shape[0] * building_mask.shape[1]
            building_ratio = building_pixels / total_pixels
            print(f"   建筑占比: {building_ratio*100:.1f}% ({building_pixels} 像素)")
            
            # 使用PIL读取原始图片（避免中文路径问题）
            from PIL import Image
            try:
                original_img_pil = Image.open(original_path)
                if original_img_pil.mode != 'RGB':
                    original_img_pil = original_img_pil.convert('RGB')

                # 转换为numpy数组（RGB格式）
                original_img = np.array(original_img_pil)
            except Exception as e:
                print(f"   ❌ 无法读取原始图片: {original_path}, 错误: {e}")
                return False

            # 调整mask尺寸以匹配原始图片
            if building_mask.shape != original_img.shape[:2]:
                # 使用PIL调整mask尺寸
                mask_pil = Image.fromarray(building_mask.astype('uint8') * 255, 'L')
                mask_resized = mask_pil.resize(original_img_pil.size, Image.Resampling.NEAREST)
                building_mask_resized = np.array(mask_resized) > 128
            else:
                building_mask_resized = building_mask
            
            filename = mask_path.stem
            
            # 方法1：白色背景版本
            result_white = original_img.copy()
            result_white[~building_mask_resized] = [255, 255, 255]
            
            white_bg_path = self.white_bg_dir / f"{filename}_building_white.jpg"
            white_img = Image.fromarray(result_white, 'RGB')
            white_img.save(str(white_bg_path), 'JPEG', quality=95)
            
            # 方法2：透明背景版本
            result_transparent = np.zeros((original_img.shape[0], original_img.shape[1], 4), dtype=np.uint8)
            result_transparent[:, :, :3] = original_img  # 已经是RGB格式
            result_transparent[:, :, 3] = 255  # 不透明
            result_transparent[~building_mask_resized, 3] = 0  # 透明
            
            transparent_path = self.transparent_dir / f"{filename}_building_transparent.png"
            pil_img = Image.fromarray(result_transparent, 'RGBA')
            pil_img.save(str(transparent_path))
            
            # 方法3：轮廓版本
            result_contour = np.zeros_like(original_img)
            result_contour[building_mask_resized] = original_img[building_mask_resized]
            
            contour_path = self.contour_dir / f"{filename}_building_contour.jpg"
            contour_img = Image.fromarray(result_contour, 'RGB')
            contour_img.save(str(contour_path), 'JPEG', quality=95)
            
            print(f"   ✅ 建筑提取完成: {mask_path.name}")
            return True
            
        except Exception as e:
            print(f"   ❌ 处理失败: {e}")
            return False
    
    def find_matching_pairs(self):
        """
        查找色块图和原始图片的匹配对
        
        返回:
            list: 匹配的文件对列表
        """
        # 查找所有可能的色块图文件
        mask_extensions = ['.png', '.jpg', '.jpeg']
        all_mask_files = []

        for ext in mask_extensions:
            all_mask_files.extend(list(self.mask_dir.glob(f"*{ext}")))
            all_mask_files.extend(list(self.mask_dir.glob(f"*{ext.upper()}")))

        # 过滤出有效的图片文件（只检查扩展名）
        mask_files = [f for f in all_mask_files if self.is_valid_image_file(f)]

        # 查找所有原始图片文件
        all_original_files = []
        for ext in mask_extensions:
            all_original_files.extend(list(self.original_dir.glob(f"*{ext}")))
            all_original_files.extend(list(self.original_dir.glob(f"*{ext.upper()}")))

        # 过滤出有效的图片文件（只检查扩展名）
        original_files = [f for f in all_original_files if self.is_valid_image_file(f)]
        
        # 创建原始图片的名称映射
        original_dict = {}
        for orig_file in original_files:
            # 尝试多种匹配方式
            base_name = orig_file.stem
            original_dict[base_name] = orig_file
            
            # 如果文件名包含坐标信息，也用坐标作为键
            if '_' in base_name:
                parts = base_name.split('_')
                if len(parts) >= 2:
                    coord_key = f"{parts[0]}_{parts[1]}"
                    original_dict[coord_key] = orig_file
        
        # 匹配文件对
        matched_pairs = []
        
        for mask_file in mask_files:
            mask_base = mask_file.stem
            
            # 尝试直接匹配
            if mask_base in original_dict:
                matched_pairs.append((mask_file, original_dict[mask_base]))
                continue
            
            # 尝试坐标匹配
            if '_' in mask_base:
                parts = mask_base.split('_')
                if len(parts) >= 2:
                    coord_key = f"{parts[0]}_{parts[1]}"
                    if coord_key in original_dict:
                        matched_pairs.append((mask_file, original_dict[coord_key]))
                        continue
            
            # 尝试模糊匹配
            for orig_name, orig_file in original_dict.items():
                if mask_base in orig_name or orig_name in mask_base:
                    matched_pairs.append((mask_file, orig_file))
                    break
        
        # 显示文件统计信息
        print(f"   找到色块图: {len(mask_files)} 张")
        print(f"   找到原始图: {len(original_files)} 张")
        print(f"   匹配成功: {len(matched_pairs)} 对")

        # 显示无效文件信息（非图片格式）
        excluded_masks = len(all_mask_files) - len(mask_files)
        excluded_originals = len(all_original_files) - len(original_files)

        if excluded_masks > 0:
            print(f"   排除非图片文件 - 色块图: {excluded_masks} 个")

        if excluded_originals > 0:
            print(f"   排除非图片文件 - 原始图: {excluded_originals} 个")
        
        return matched_pairs
    
    def process_all_masks(self):
        """批量处理所有色块图"""
        print(f"\n🔄 开始批量处理色块图...")
        
        # 查找匹配的文件对
        matched_pairs = self.find_matching_pairs()
        
        if not matched_pairs:
            print("❌ 没有找到匹配的文件对")
            return
        
        # 处理统计
        success_count = 0
        failed_count = 0
        
        # 处理每对文件
        for i, (mask_path, original_path) in enumerate(matched_pairs, 1):
            print(f"\n进度: {i}/{len(matched_pairs)}")
            print(f"   色块图: {mask_path.name}")
            print(f"   原始图: {original_path.name}")
            
            if self.extract_building_from_mask(mask_path, original_path):
                success_count += 1
            else:
                failed_count += 1
        
        # 生成处理报告
        self._generate_report(success_count, failed_count, len(matched_pairs))
        
        print(f"\n🎉 批量处理完成!")
        print(f"   成功处理: {success_count} 对")
        print(f"   失败数量: {failed_count} 对")
        print(f"   成功率: {success_count/len(matched_pairs)*100:.1f}%")
        print(f"\n📁 结果保存在:")
        print(f"   白色背景: {self.white_bg_dir}")
        print(f"   透明背景: {self.transparent_dir}")
        print(f"   轮廓版本: {self.contour_dir}")
        print(f"   颜色分析: {self.mask_analysis_dir}")
    
    def _generate_report(self, success_count, failed_count, total_count):
        """生成处理报告"""
        report_content = f"""色块图建筑提取报告
{'='*50}

处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
色块图目录: {self.mask_dir}
原始图目录: {self.original_dir}
输出目录: {self.output_dir}

处理统计:
- 总文件对数: {total_count}
- 成功提取: {success_count}
- 失败数量: {failed_count}
- 成功率: {success_count/total_count*100:.1f}%

建筑识别颜色:
"""
        
        for class_name, color in self.building_colors.items():
            report_content += f"- {class_name}: RGB{color}\n"
        
        report_content += f"""
输出文件类型:
1. 白色背景版本: 非建筑部分为白色
2. 透明背景版本: 非建筑部分透明（PNG格式）
3. 轮廓版本: 只显示建筑轮廓，其他为黑色
4. 颜色分析: 每张图的颜色分布JSON文件

颜色匹配参数:
- 颜色容差: {self.color_tolerance}
"""
        
        report_file = self.output_dir / f"mask_processing_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"📋 处理报告: {report_file}")


def main():
    """主函数"""
    print("🎨 色块图建筑提取器")
    print("="*50)
    print("从语义分割色块图中快速提取建筑部分")
    print("="*50)
    
    # 获取色块图目录
    mask_dir = input("请输入分割色块图目录路径: ").strip().strip('"')
    if not mask_dir or not os.path.exists(mask_dir):
        print("❌ 色块图目录无效")
        return False
    
    # 获取原始图片目录
    original_dir = input("请输入原始图片目录路径: ").strip().strip('"')
    if not original_dir or not os.path.exists(original_dir):
        print("❌ 原始图片目录无效")
        return False
    
    try:
        # 创建处理器
        processor = SegmentationMaskProcessor(mask_dir, original_dir)
        
        # 开始处理
        processor.process_all_masks()
        
        # 询问是否打开输出目录
        open_folder = input("\n是否打开输出目录查看结果？(y/n): ").strip().lower()
        if open_folder == 'y':
            import subprocess
            import platform
            
            if platform.system() == 'Windows':
                subprocess.run(['explorer', str(processor.output_dir)])
            elif platform.system() == 'Darwin':  # macOS
                subprocess.run(['open', str(processor.output_dir)])
            else:  # Linux
                subprocess.run(['xdg-open', str(processor.output_dir)])
        
        return True
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    try:
        success = main()
        print(f"\n{'✅ 处理完成' if success else '❌ 处理失败'}")
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")
