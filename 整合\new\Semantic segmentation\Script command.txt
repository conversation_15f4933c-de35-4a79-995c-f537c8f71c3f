#anaconda创建新的虚拟环境
conda create -n mxnet python=3.7

#激活新的虚拟环境
activate mxnet

#查看虚拟环境的位置
conda info --envs

#安装各种所需的包（2024年最新：这里与视频中讲解有出入，一定要用下面这几条命令！！！）
pip install  mxnet
pip install  pandas
pip install matplotlib
pip install  opencv_python
pip install  gluoncv
pip install matplotlib==3.5.1



#安装各种所需的包 清华源镜像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ --upgrade mxnet
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ --upgrade pandas
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ --upgrade matplotlib
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ --upgrade opencv_python
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ --upgrade gluoncv
pip install matplotlib==3.5.1 -i https://pypi.tuna.tsinghua.edu.cn/simple


# 添加新的镜像
conda config --add channels https://mirrors.ustc.edu.cn/anaconda/pkgs/main/
conda config --add channels https://mirrors.ustc.edu.cn/anaconda/pkgs/free/
conda config --add channels https://mirrors.ustc.edu.cn/anaconda/cloud/conda-forge/
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
conda config --set show_channel_urls yes



