#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速街景图片拼接脚本
简化版本，快速将4个方向的街景图片拼接成一组

使用方法:
python quick_stitch_streetview.py "图片目录路径"
"""

import os
import sys
from pathlib import Path
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from collections import defaultdict

def quick_stitch_streetview_images(input_dir, output_dir=None):
    """
    快速拼接街景图片
    
    参数:
        input_dir (str): 输入图片目录
        output_dir (str): 输出目录，默认为输入目录下的stitched文件夹
    
    返回:
        dict: 处理结果统计
    """
    input_path = Path(input_dir)
    
    if output_dir is None:
        output_path = input_path.parent / "stitched_streetview"
    else:
        output_path = Path(output_dir)
    
    output_path.mkdir(parents=True, exist_ok=True)
    
    print(f"🔄 快速拼接街景图片...")
    print(f"📁 输入目录: {input_path}")
    print(f"📁 输出目录: {output_path}")
    
    # 查找所有PNG图片
    image_files = list(input_path.glob("*.png"))
    image_files.extend(list(input_path.glob("*.jpg")))
    image_files.extend(list(input_path.glob("*.jpeg")))
    
    print(f"   找到图片: {len(image_files)} 张")
    
    if len(image_files) == 0:
        print("❌ 没有找到图片文件")
        return {'success': 0, 'failed': 0, 'total': 0}
    
    # 按坐标分组
    grouped_images = defaultdict(dict)
    
    for image_file in image_files:
        try:
            # 解析文件名：经度_纬度_方向_俯仰角.png
            filename = image_file.stem
            parts = filename.split('_')
            
            if len(parts) >= 4:
                longitude = float(parts[0])
                latitude = float(parts[1])
                direction = int(parts[2])
                
                coord_key = f"{longitude}_{latitude}"
                grouped_images[coord_key][direction] = image_file
                
        except (ValueError, IndexError):
            print(f"   ⚠️  跳过文件: {image_file.name}")
            continue
    
    print(f"   坐标点数量: {len(grouped_images)}")
    
    # 拼接处理
    success_count = 0
    failed_count = 0
    
    for coord_key, directions in grouped_images.items():
        try:
            # 检查是否有4个方向
            required_directions = [0, 90, 180, 270]
            if not all(d in directions for d in required_directions):
                print(f"   ⚠️  {coord_key} 方向不完整，跳过")
                failed_count += 1
                continue
            
            # 加载4张图片
            images = {}
            for direction in required_directions:
                img = Image.open(directions[direction])
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                images[direction] = img
            
            # 获取图片尺寸
            img_width, img_height = images[0].size
            
            # 创建拼接画布 (2x2布局)
            canvas_width = img_width * 2
            canvas_height = img_height * 2
            stitched_image = Image.new('RGB', (canvas_width, canvas_height), 'white')
            
            # 拼接图片
            # 布局：北(0°) 东(90°)
            #      西(270°) 南(180°)
            stitched_image.paste(images[0], (0, 0))              # 北-左上
            stitched_image.paste(images[90], (img_width, 0))     # 东-右上
            stitched_image.paste(images[270], (0, img_height))   # 西-左下
            stitched_image.paste(images[180], (img_width, img_height))  # 南-右下
            
            # 添加简单的分割线
            draw = ImageDraw.Draw(stitched_image)
            line_color = (255, 255, 255)  # 白色
            line_width = 2
            
            # 垂直线
            draw.line([(img_width, 0), (img_width, canvas_height)], 
                     fill=line_color, width=line_width)
            # 水平线
            draw.line([(0, img_height), (canvas_width, img_height)], 
                     fill=line_color, width=line_width)
            
            # 保存拼接图片
            output_filename = f"stitched_{coord_key}.png"
            output_file = output_path / output_filename
            stitched_image.save(output_file, 'PNG', quality=95)
            
            # 关闭图片释放内存
            for img in images.values():
                img.close()
            stitched_image.close()
            
            print(f"   ✅ {coord_key} -> {output_filename}")
            success_count += 1
            
        except Exception as e:
            print(f"   ❌ {coord_key} 拼接失败: {e}")
            failed_count += 1
            continue
    
    # 输出结果
    total_groups = len(grouped_images)
    print(f"\n🎉 拼接完成!")
    print(f"   总坐标点: {total_groups}")
    print(f"   成功拼接: {success_count}")
    print(f"   失败数量: {failed_count}")
    print(f"   成功率: {success_count/total_groups*100:.1f}%")
    print(f"   输出目录: {output_path}")
    
    return {
        'success': success_count,
        'failed': failed_count,
        'total': total_groups
    }

def main():
    """主函数"""
    print("🚀 快速街景图片拼接工具")
    print("="*50)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        input_dir = sys.argv[1]
    else:
        # 交互式输入
        input_dir = input("请输入街景图片目录路径: ").strip().strip('"')
    
    if not input_dir:
        print("❌ 请提供图片目录路径")
        return False
    
    if not os.path.exists(input_dir):
        print(f"❌ 目录不存在: {input_dir}")
        return False
    
    if not os.path.isdir(input_dir):
        print(f"❌ 不是有效目录: {input_dir}")
        return False
    
    try:
        # 执行拼接
        result = quick_stitch_streetview_images(input_dir)
        
        if result['success'] > 0:
            print(f"\n✅ 成功拼接 {result['success']} 组图片")
            
            # 询问是否打开输出目录
            open_folder = input("是否打开输出目录查看结果？(y/n): ").strip().lower()
            if open_folder == 'y':
                output_dir = Path(input_dir).parent / "stitched_streetview"
                
                import subprocess
                import platform
                
                if platform.system() == 'Windows':
                    subprocess.run(['explorer', str(output_dir)])
                elif platform.system() == 'Darwin':  # macOS
                    subprocess.run(['open', str(output_dir)])
                else:  # Linux
                    subprocess.run(['xdg-open', str(output_dir)])
        
        return result['success'] > 0
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        print(f"\n{'✅ 处理完成' if success else '❌ 处理失败'}")
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")
