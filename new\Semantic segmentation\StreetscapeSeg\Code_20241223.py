import os
from PIL import Image
import numpy as np
import matplotlib.pyplot as plt
from sklearn.cluster import KMeans

def analyze_image_color(image_path):
    # 打开图片并转换为RGB模式
    image = Image.open(image_path).convert('RGB')
    # 将图片转换为numpy数组
    image_array = np.array(image)
    # 重塑数组形状，将每个像素点的RGB值作为一个样本
    pixels = image_array.reshape(-1, 3)
    # 使用KMeans算法聚类
    kmeans = KMeans(n_clusters=5, random_state=0)
    kmeans.fit(pixels)
    # 获取聚类中心，即主要颜色
    main_colors = kmeans.cluster_centers_.astype(int)
    # 统计每个聚类的样本数量
    labels, counts = np.unique(kmeans.labels_, return_counts=True)
    # 计算每种主要颜色的占比
    percentages = counts / counts.sum() * 100
    return main_colors, percentages

def batch_analyze_images(folder_path):
    all_main_colors = []
    all_percentages = []
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.endswith(('.jpg', '.png', '.jpeg')):
                image_path = os.path.join(root, file)
                main_colors, percentages = analyze_image_color(image_path)
                all_main_colors.append(main_colors)
                all_percentages.append(percentages)
    return all_main_colors, all_percentages

# 指定照片文件夹路径
folder_path = 'your_folder_path'
main_colors_list, percentages_list = batch_analyze_images(folder_path)

# 打印结果
for i in range(len(main_colors_list)):
    print(f"Image {i + 1}:")
    for j in range(len(main_colors_list[i])):
        print(f"Main color {j + 1}: RGB({main_colors_list[i][j][0]}, {main_colors_list[i][j][1]}, {main_colors_list[i][j][2]}) - Percentage: {percentages_list[i][j]:.2f}%")
     