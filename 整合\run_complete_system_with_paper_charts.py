#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
完整系统运行脚本 - 包含论文图表生成
"""

import os
import sys
import logging
import time

# 添加项目根目录到路径
sys.path.append(os.path.abspath('.'))

def run_complete_system():
    """运行完整的建筑立面色彩优化系统"""
    print("=" * 80)
    print("建筑立面色彩优化系统 - 完整版（包含论文图表生成）")
    print("=" * 80)
    
    start_time = time.time()
    
    # 设置日志
    logging.basicConfig(level=logging.INFO, 
                       format='%(asctime)s - %(levelname)s - %(message)s')
    
    try:
        # 导入主函数
        from src.main import main
        
        print("🚀 启动完整系统...")
        print("系统将依次执行以下步骤:")
        print("  1. 图像预处理")
        print("  2. 数据处理（聚类分析）")
        print("  3. 色彩生成（渐变和插入）")
        print("  4. 评估系统")
        print("  5. 可视化图表生成")
        print("  6. 论文专业图表生成 ⭐")
        print("  7. 数据导出")
        print()
        
        # 运行主函数
        main()
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print("\n" + "=" * 80)
        print("🎉 系统运行完成！")
        print(f"⏱️  总执行时间: {execution_time:.2f} 秒")
        print()
        
        # 显示生成的文件结构
        print("📁 生成的文件结构:")
        output_dir = "./output"
        if os.path.exists(output_dir):
            for root, dirs, files in os.walk(output_dir):
                level = root.replace(output_dir, '').count(os.sep)
                indent = ' ' * 2 * level
                print(f"{indent}{os.path.basename(root)}/")
                subindent = ' ' * 2 * (level + 1)
                for file in files[:5]:  # 只显示前5个文件
                    print(f"{subindent}{file}")
                if len(files) > 5:
                    print(f"{subindent}... 还有 {len(files) - 5} 个文件")
        
        print("\n🔍 重要输出目录:")
        important_dirs = [
            ("可视化结果", "./output/visualization"),
            ("论文图表", "./output/paper_charts"),
            ("聚类结果", "./output/clustering"),
            ("色彩方案", "./output/color_schemes"),
            ("评估结果", "./output/evaluation"),
            ("数据导出", "./output/exports")
        ]
        
        for name, path in important_dirs:
            if os.path.exists(path):
                file_count = len([f for f in os.listdir(path) if os.path.isfile(os.path.join(path, f))])
                print(f"  📊 {name}: {path} ({file_count} 个文件)")
            else:
                print(f"  ❌ {name}: {path} (目录不存在)")
        
        print("\n✨ 特别说明:")
        print("  🎨 论文图表包含:")
        print("     - 方法论流程图")
        print("     - 算法对比分析图")
        print("     - 3D色彩空间可视化图")
        print("     - 建筑类型色彩分析图")
        print("     - 系统验证分析图")
        
        print("\n📋 使用建议:")
        print("  1. 查看 ./output/visualization/ 目录获取主要可视化结果")
        print("  2. 查看 ./output/paper_charts/ 目录获取学术论文图表")
        print("  3. 查看 paper_charts_summary.md 获取图表生成报告")
        print("  4. 最佳色彩方案保存在 best_scheme_*.png 文件中")
        
        print("=" * 80)
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断了程序执行")
    except Exception as e:
        print(f"\n❌ 系统运行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_paper_charts_only():
    """仅测试论文图表生成功能"""
    print("=" * 60)
    print("测试论文图表生成功能")
    print("=" * 60)
    
    try:
        from src.visualization.paper_charts_generator import PaperChartsGenerator
        
        # 创建生成器
        output_dir = "./output/paper_charts_test"
        generator = PaperChartsGenerator(output_dir=output_dir)
        
        print("🎨 开始生成论文图表...")
        results = generator.generate_all_paper_charts()
        
        success_count = sum(1 for result in results.values() if result is not None)
        print(f"\n✅ 论文图表生成完成！")
        print(f"📊 成功生成: {success_count}/{len(results)} 个图表")
        print(f"📁 保存位置: {output_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 论文图表生成失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("请选择运行模式:")
    print("1. 运行完整系统（包含论文图表生成）")
    print("2. 仅测试论文图表生成")
    print("3. 退出")
    
    while True:
        choice = input("\n请输入选择 (1/2/3): ").strip()
        
        if choice == "1":
            print("\n🚀 启动完整系统...")
            success = run_complete_system()
            break
        elif choice == "2":
            print("\n🎨 启动论文图表测试...")
            success = test_paper_charts_only()
            break
        elif choice == "3":
            print("👋 再见！")
            sys.exit(0)
        else:
            print("❌ 无效选择，请输入 1、2 或 3")
    
    if success:
        print("\n🎉 程序执行成功！")
    else:
        print("\n❌ 程序执行失败！")