#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复的网络图可视化模块
基于真实的三色组合共现数据生成网络图
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
import networkx as nx
from collections import defaultdict
import itertools
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))
from src.utils import ensure_dir

class FixedNetworkGraphGenerator:
    """修复的网络图生成器"""
    
    def __init__(self, output_dir="./output/network_graphs"):
        self.output_dir = Path(output_dir)
        ensure_dir(str(self.output_dir))
        
        # SCI期刊样式设置
        self._setup_sci_style()
        
        # 专业配色方案
        self.colors = {
            'primary': '#2C3E50',
            'secondary': '#34495E', 
            'accent1': '#E74C3C',
            'accent2': '#3498DB',
            'accent3': '#27AE60',
            'accent4': '#F39C12',
            'neutral1': '#7F8C8D',
            'neutral2': '#95A5A6',
            'background': '#FFFFFF',
            'text': '#2C3E50'
        }
    
    def _setup_sci_style(self):
        """设置SCI期刊样式"""
        plt.rcParams.update({
            'font.family': ['Times New Roman', 'serif'],
            'font.size': 10,
            'axes.titlesize': 12,
            'axes.labelsize': 11,
            'xtick.labelsize': 9,
            'ytick.labelsize': 9,
            'legend.fontsize': 9,
            'figure.titlesize': 14,
            'axes.linewidth': 1.0,
            'grid.linewidth': 0.5,
            'lines.linewidth': 1.5,
            'patch.linewidth': 0.8,
            'axes.edgecolor': '#2C3E50',
            'axes.facecolor': 'white',
            'figure.facecolor': 'white',
            'text.color': '#2C3E50',
            'axes.grid': True,
            'grid.alpha': 0.3,
            'grid.color': '#ECF0F1',
            'axes.axisbelow': True,
            'savefig.dpi': 300,
            'savefig.bbox': 'tight',
            'savefig.facecolor': 'white',
            'savefig.pad_inches': 0.1,
            'axes.unicode_minus': False,
            'axes.spines.top': False,
            'axes.spines.right': False
        })
    
    def generate_cooccurrence_network_graph(self, cluster_colors, triplet_cooccurrence, 
                                          top_triplet_indices, output_filename="network_graph_fixed.png"):
        """生成基于共现数据的网络图"""
        print("🔄 生成修复的网络图...")
        
        # 创建图表
        fig = plt.figure(figsize=(20, 14))
        gs = gridspec.GridSpec(3, 3, height_ratios=[2, 1, 1], width_ratios=[2, 1, 1])
        
        # 主网络图
        ax_main = fig.add_subplot(gs[0, :])
        self._plot_main_network(ax_main, cluster_colors, triplet_cooccurrence, top_triplet_indices)
        
        # 统计信息
        ax_stats = fig.add_subplot(gs[1, 0])
        self._plot_cooccurrence_statistics(ax_stats, triplet_cooccurrence)
        
        # 颜色调色板
        ax_palette = fig.add_subplot(gs[1, 1])
        self._plot_color_palette(ax_palette, cluster_colors, top_triplet_indices)
        
        # 共现分布
        ax_dist = fig.add_subplot(gs[1, 2])
        self._plot_cooccurrence_distribution(ax_dist, triplet_cooccurrence)
        
        # 最佳三色组合展示
        ax_triplet = fig.add_subplot(gs[2, :])
        self._plot_best_triplet_showcase(ax_triplet, cluster_colors, triplet_cooccurrence, top_triplet_indices)
        
        plt.tight_layout()
        
        # 保存图表
        output_path = self.output_dir / output_filename
        fig.savefig(output_path, dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        plt.close(fig)
        
        print(f"✅ 修复的网络图已保存: {output_path}")
        return str(output_path)
    
    def _plot_main_network(self, ax, cluster_colors, triplet_cooccurrence, top_triplet_indices):
        """绘制主网络图"""
        G = nx.Graph()
        
        # 添加所有颜色节点
        for i in range(len(cluster_colors)):
            G.add_node(i, color=cluster_colors[i])
        
        # 基于三色组合共现数据添加边
        edge_weights = defaultdict(int)
        
        # 从三色组合中提取两两关系
        for triplet, count in triplet_cooccurrence.items():
            if count > 0:
                # 为三色组合中的每对颜色添加边权重
                for i, j in itertools.combinations(triplet, 2):
                    edge_weights[(i, j)] += count
        
        # 添加边到图中
        for (i, j), weight in edge_weights.items():
            G.add_edge(i, j, weight=weight)
        
        print(f"   网络图节点数: {G.number_of_nodes()}")
        print(f"   网络图边数: {G.number_of_edges()}")
        
        # 使用改进的布局算法
        if G.number_of_edges() > 0:
            pos = nx.spring_layout(G, k=3, iterations=100, seed=42)
        else:
            pos = nx.circular_layout(G)
        
        # 绘制节点
        node_colors = [cluster_colors[i]/255 for i in range(len(cluster_colors))]
        
        # 节点大小基于参与的三色组合数量
        node_participation = defaultdict(int)
        for triplet, count in triplet_cooccurrence.items():
            if count > 0:
                for color_idx in triplet:
                    node_participation[color_idx] += count
        
        max_participation = max(node_participation.values()) if node_participation else 1
        node_sizes = [800 + 800 * (node_participation.get(i, 0) / max_participation)
                     for i in range(len(cluster_colors))]
        
        # 高亮最佳三色组合的节点
        node_edge_colors = []
        node_edge_widths = []
        for i in range(len(cluster_colors)):
            if i in top_triplet_indices:
                node_edge_colors.append('red')
                node_edge_widths.append(4)
            else:
                node_edge_colors.append('black')
                node_edge_widths.append(2)
        
        nx.draw_networkx_nodes(G, pos, node_color=node_colors, node_size=node_sizes,
                             edgecolors=node_edge_colors, linewidths=node_edge_widths, ax=ax)
        
        # 绘制边
        if G.number_of_edges() > 0:
            edges = G.edges()
            weights = [G[u][v]['weight'] for u, v in edges]
            
            max_weight = max(weights)
            min_weight = min(weights)
            
            edge_widths = []
            edge_colors = []
            edge_alphas = []
            
            for weight in weights:
                if max_weight > min_weight:
                    normalized = (weight - min_weight) / (max_weight - min_weight)
                    width = 1 + normalized * 8  # 线条宽度1-9
                    color_intensity = 0.3 + normalized * 0.7  # 颜色强度0.3-1.0
                    alpha = 0.5 + normalized * 0.4  # 透明度0.5-0.9
                else:
                    width = 5
                    color_intensity = 0.6
                    alpha = 0.7
                
                edge_widths.append(width)
                edge_colors.append(plt.cm.Reds(color_intensity))
                edge_alphas.append(alpha)
            
            # 分别绘制不同权重的边
            for i, (edge, width, color, alpha) in enumerate(zip(edges, edge_widths, edge_colors, edge_alphas)):
                nx.draw_networkx_edges(G, pos, edgelist=[edge], width=[width],
                                     edge_color=[color], alpha=alpha, ax=ax)
        
        # 添加节点标签
        labels = {i: f'C{i+1}' for i in range(len(cluster_colors))}
        nx.draw_networkx_labels(G, pos, labels, font_size=11,
                              font_weight='bold', font_color='white', ax=ax)
        
        # 添加图例
        legend_elements = [
            plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='red', 
                      markersize=12, markeredgecolor='red', markeredgewidth=3,
                      label='Best Triplet Colors', linestyle='None'),
            plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='gray', 
                      markersize=10, markeredgecolor='black', markeredgewidth=2,
                      label='Other Colors', linestyle='None'),
            plt.Line2D([0], [0], color='red', linewidth=6, alpha=0.8, label='High Cooccurrence'),
            plt.Line2D([0], [0], color='red', linewidth=2, alpha=0.5, label='Low Cooccurrence')
        ]
        
        ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1, 1))
        
        ax.set_title('Color Cooccurrence Network Graph (Based on Triplet Analysis)', 
                    fontweight='bold', fontsize=14)
        ax.axis('off')
        self._add_subplot_label(ax, 'A')
    
    def _plot_cooccurrence_statistics(self, ax, triplet_cooccurrence):
        """绘制共现统计信息"""
        # 统计数据
        total_triplets = len(triplet_cooccurrence)
        active_triplets = sum(1 for count in triplet_cooccurrence.values() if count > 0)
        max_cooccurrence = max(triplet_cooccurrence.values()) if triplet_cooccurrence else 0
        avg_cooccurrence = np.mean([count for count in triplet_cooccurrence.values() if count > 0]) if active_triplets > 0 else 0
        
        stats_text = f"""Cooccurrence Statistics

Total Triplets: {total_triplets}
Active Triplets: {active_triplets}
Max Cooccurrence: {max_cooccurrence}
Avg Cooccurrence: {avg_cooccurrence:.2f}
Activity Rate: {active_triplets/total_triplets*100:.1f}%"""
        
        ax.text(0.05, 0.95, stats_text, transform=ax.transAxes, fontsize=11,
               verticalalignment='top', fontfamily='monospace',
               bbox=dict(boxstyle="round,pad=0.5", facecolor='lightgray', alpha=0.8))
        
        ax.set_title('Network Statistics', fontweight='bold')
        ax.axis('off')
        self._add_subplot_label(ax, 'B')
    
    def _plot_color_palette(self, ax, cluster_colors, top_triplet_indices):
        """绘制颜色调色板"""
        n_colors = len(cluster_colors)
        
        for i, color in enumerate(cluster_colors):
            # 颜色块
            rect = plt.Rectangle((i, 0), 1, 1, facecolor=color/255.0, 
                               edgecolor='red' if i in top_triplet_indices else 'black',
                               linewidth=3 if i in top_triplet_indices else 1)
            ax.add_patch(rect)
            
            # 标签
            text_color = 'white' if np.mean(color) < 128 else 'black'
            ax.text(i + 0.5, 0.5, f'C{i+1}', ha='center', va='center',
                   fontweight='bold', color=text_color, fontsize=10)
        
        ax.set_xlim(0, n_colors)
        ax.set_ylim(0, 1)
        ax.set_aspect('equal')
        ax.axis('off')
        ax.set_title('Color Palette (Red Border = Best Triplet)', fontweight='bold')
        self._add_subplot_label(ax, 'C')
    
    def _plot_cooccurrence_distribution(self, ax, triplet_cooccurrence):
        """绘制共现分布直方图"""
        counts = [count for count in triplet_cooccurrence.values() if count > 0]
        
        if counts:
            ax.hist(counts, bins=min(20, len(set(counts))), alpha=0.7, 
                   color=self.colors['accent2'], edgecolor='black')
            ax.set_xlabel('Cooccurrence Count')
            ax.set_ylabel('Frequency')
            ax.set_title('Cooccurrence Distribution', fontweight='bold')
            ax.grid(True, alpha=0.3)
        else:
            ax.text(0.5, 0.5, 'No Cooccurrence Data', ha='center', va='center',
                   transform=ax.transAxes, fontsize=12)
            ax.set_title('Cooccurrence Distribution', fontweight='bold')
        
        self._add_subplot_label(ax, 'D')
    
    def _plot_best_triplet_showcase(self, ax, cluster_colors, triplet_cooccurrence, top_triplet_indices):
        """展示最佳三色组合"""
        if not top_triplet_indices or len(top_triplet_indices) != 3:
            ax.text(0.5, 0.5, 'No Valid Triplet Found', ha='center', va='center',
                   transform=ax.transAxes, fontsize=14)
            ax.set_title('Best Color Triplet Showcase', fontweight='bold')
            ax.axis('off')
            return
        
        # 获取最佳三色组合的共现次数
        best_triplet_tuple = tuple(sorted(top_triplet_indices))
        best_count = triplet_cooccurrence.get(best_triplet_tuple, 0)
        
        # 绘制三个颜色块
        colors = [cluster_colors[i] for i in top_triplet_indices]
        
        for i, (color_idx, color) in enumerate(zip(top_triplet_indices, colors)):
            x = i * 3
            
            # 大颜色块
            rect = plt.Rectangle((x, 1), 2.5, 2, facecolor=color/255.0, 
                               edgecolor='black', linewidth=2)
            ax.add_patch(rect)
            
            # 颜色信息
            text_color = 'white' if np.mean(color) < 128 else 'black'
            ax.text(x + 1.25, 2, f'C{color_idx+1}', ha='center', va='center',
                   fontweight='bold', color=text_color, fontsize=16)
            
            ax.text(x + 1.25, 0.5, f'RGB{tuple(map(int, color))}', ha='center', va='center',
                   fontweight='bold', fontsize=10)
        
        # 连接线和共现次数
        ax.plot([1.25, 4.25], [3.5, 3.5], 'k-', linewidth=2)
        ax.plot([4.25, 7.25], [3.5, 3.5], 'k-', linewidth=2)
        
        ax.text(4.25, 4, f'Best Triplet\nCooccurrence: {best_count} times', 
               ha='center', va='center', fontweight='bold', fontsize=12,
               bbox=dict(boxstyle="round,pad=0.5", facecolor='yellow', alpha=0.8))
        
        ax.set_xlim(-0.5, 9.5)
        ax.set_ylim(0, 5)
        ax.set_aspect('equal')
        ax.axis('off')
        ax.set_title('Best Color Triplet Showcase (Highest Cooccurrence)', fontweight='bold', fontsize=14)
        self._add_subplot_label(ax, 'E')
    
    def _add_subplot_label(self, ax, label, x=-0.1, y=1.05):
        """添加子图标签"""
        ax.text(x, y, label, transform=ax.transAxes, fontsize=14, fontweight='bold',
               bbox=dict(boxstyle="round,pad=0.3", facecolor='white', edgecolor='black'))
