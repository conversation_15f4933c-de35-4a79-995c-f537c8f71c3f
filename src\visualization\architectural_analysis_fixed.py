#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复版建筑色彩应用分析图生成器
解决重复方法定义和数据缺失问题
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Rectangle, FancyBboxPatch
import seaborn as sns
from scipy import stats
import matplotlib.gridspec as gridspec
import logging

class ArchitecturalAnalysisFixed:
    """修复版建筑色彩应用分析图生成器"""
    
    def __init__(self, config=None):
        self.logger = logging.getLogger(__name__)
        
        # 默认建筑专业配色方案
        self.default_colors = {
            'facade': '#8B4513',       # 建筑棕
            'environment': '#228B22',   # 环境绿
            'material': '#696969',      # 材质灰
            'seasonal': '#FF6347',      # 季节橙
            'lighting': '#FFD700',      # 光照金
            'urban': '#4682B4',         # 城市蓝
            'sustainability': '#32CD32', # 可持续绿
            'performance': '#9370DB'    # 性能紫
        }
        
        # 当前使用的配色方案
        self.colors = self.default_colors.copy()
        
        self.architectural_metrics = [
            'Environmental Adaptation',
            'Material Compatibility', 
            'Seasonal Variation',
            'Lighting Performance',
            'Urban Integration',
            'Sustainability Score'
        ]
    
    def generate_architectural_analysis(self, schemes_data, output_path):
        """生成建筑色彩应用分析图"""
        try:
            self.logger.info("开始生成建筑色彩应用分析图...")
            
            # 基于最佳方案颜色生成配色方案
            if schemes_data and len(schemes_data) > 0:
                best_colors = schemes_data[0].get('colors', None)
                if best_colors is not None:
                    self.colors = self._generate_color_scheme_from_best_colors(best_colors)
                    self.logger.info("已基于最佳方案颜色生成建筑配色方案")
            
            # 生成建筑数据
            architectural_data = self._generate_architectural_data(schemes_data)
            
            # 创建专业建筑分析图表
            fig = plt.figure(figsize=(24, 18), dpi=300)
            gs = gridspec.GridSpec(3, 4, height_ratios=[1, 1, 1], width_ratios=[1, 1, 1, 1],
                                 hspace=0.4, wspace=0.3)
            
            # 子图A: 建筑立面色彩分布分析
            ax1 = fig.add_subplot(gs[0, :2])
            self._plot_facade_distribution(ax1, architectural_data)
            self._add_subplot_label(ax1, 'A')
            
            # 子图B: 环境适应性评估
            ax2 = fig.add_subplot(gs[0, 2:])
            self._plot_environmental_adaptation(ax2, architectural_data)
            self._add_subplot_label(ax2, 'B')
            
            # 子图C: 材质兼容性分析
            ax3 = fig.add_subplot(gs[1, 0])
            self._plot_material_compatibility(ax3, architectural_data)
            self._add_subplot_label(ax3, 'C')
            
            # 子图D: 季节性色彩变化模拟
            ax4 = fig.add_subplot(gs[1, 1])
            self._plot_seasonal_variation(ax4, architectural_data)
            self._add_subplot_label(ax4, 'D')
            
            # 子图E: 光照条件下的色彩表现
            ax5 = fig.add_subplot(gs[1, 2])
            self._plot_lighting_performance(ax5, architectural_data)
            self._add_subplot_label(ax5, 'E')
            
            # 子图F: 城市景观融合度评估
            ax6 = fig.add_subplot(gs[1, 3])
            self._plot_urban_integration(ax6, architectural_data)
            self._add_subplot_label(ax6, 'F')
            
            # 子图G: 建筑色彩可持续性指标
            ax7 = fig.add_subplot(gs[2, 0])
            self._plot_sustainability_metrics(ax7, architectural_data)
            self._add_subplot_label(ax7, 'G')
            
            # 子图H: 综合建筑性能对比
            ax8 = fig.add_subplot(gs[2, 1:])
            self._plot_comprehensive_performance(ax8, architectural_data)
            self._add_subplot_label(ax8, 'H')
            
            plt.tight_layout(pad=3.0)
            fig.savefig(output_path, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            plt.close(fig)
            
            self.logger.info(f"建筑色彩应用分析图生成成功: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"生成建筑分析图失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def _generate_architectural_data(self, schemes_data):
        """生成建筑数据"""
        architectural_data = {
            'facade_colors': [],
            'environmental_adaptation': [],
            'material_compatibility': [],
            'seasonal_variation': [],
            'lighting_performance': [],
            'urban_integration': [],
            'sustainability_score': []
        }
        
        for scheme in schemes_data:
            # 立面颜色
            colors = scheme.get('colors', np.random.randint(50, 200, (8, 3)))
            architectural_data['facade_colors'].append(colors)
            
            # 环境适应性 (基于颜色的自然度)
            env_score = self._calculate_environmental_score(colors)
            architectural_data['environmental_adaptation'].append(env_score)
            
            # 材质兼容性 (基于颜色的材质适配度)
            material_score = self._calculate_material_score(colors)
            architectural_data['material_compatibility'].append(material_score)
            
            # 季节性变化 (基于颜色的季节适应性)
            seasonal_score = self._calculate_seasonal_score(colors)
            architectural_data['seasonal_variation'].append(seasonal_score)
            
            # 光照性能 (基于颜色的光照反射特性)
            lighting_score = self._calculate_lighting_score(colors)
            architectural_data['lighting_performance'].append(lighting_score)
            
            # 城市融合度 (基于颜色的城市环境适配)
            urban_score = self._calculate_urban_score(colors)
            architectural_data['urban_integration'].append(urban_score)
            
            # 可持续性评分 (基于颜色的环保特性)
            sustainability_score = self._calculate_sustainability_score(colors)
            architectural_data['sustainability_score'].append(sustainability_score)
        
        return architectural_data
    
    def _calculate_environmental_score(self, colors):
        """计算环境适应性评分"""
        # 基于颜色与自然色彩的相似度
        natural_colors = np.array([[139, 69, 19], [34, 139, 34], [70, 130, 180]])  # 棕、绿、蓝
        
        if isinstance(colors, list):
            colors = np.array(colors)
        
        scores = []
        for color in colors[:5]:  # 取前5个颜色
            distances = [np.linalg.norm(color - nat_color) for nat_color in natural_colors]
            min_distance = min(distances)
            score = max(0, 1 - min_distance / 255)  # 归一化到0-1
            scores.append(score)
        
        return np.mean(scores)
    
    def _calculate_material_score(self, colors):
        """计算材质兼容性评分"""
        # 基于颜色与常见建筑材质的匹配度
        material_colors = np.array([[139, 69, 19], [105, 105, 105], [210, 180, 140]])  # 木材、混凝土、石材
        
        if isinstance(colors, list):
            colors = np.array(colors)
        
        scores = []
        for color in colors[:5]:
            distances = [np.linalg.norm(color - mat_color) for mat_color in material_colors]
            min_distance = min(distances)
            score = max(0, 1 - min_distance / 255)
            scores.append(score)
        
        return np.mean(scores)
    
    def _calculate_seasonal_score(self, colors):
        """计算季节性适应评分"""
        # 基于颜色的季节适应性
        if isinstance(colors, list):
            colors = np.array(colors)
        
        # 计算颜色的温度感知
        warm_colors = colors[:, 0] > colors[:, 2]  # 红色 > 蓝色
        warm_ratio = np.mean(warm_colors)
        
        # 季节适应性 = 温暖色彩比例的平衡度
        return 1 - abs(warm_ratio - 0.5) * 2
    
    def _calculate_lighting_score(self, colors):
        """计算光照性能评分"""
        # 基于颜色的亮度和反射特性
        if isinstance(colors, list):
            colors = np.array(colors)
        
        # 计算平均亮度
        brightness = np.mean(colors, axis=1)
        avg_brightness = np.mean(brightness)
        
        # 理想亮度范围是100-180
        ideal_brightness = 140
        score = 1 - abs(avg_brightness - ideal_brightness) / 140
        
        return max(0, min(1, score))
    
    def _calculate_urban_score(self, colors):
        """计算城市融合度评分"""
        # 基于颜色与城市环境的协调性
        urban_colors = np.array([[128, 128, 128], [105, 105, 105], [169, 169, 169]])  # 城市灰色调
        
        if isinstance(colors, list):
            colors = np.array(colors)
        
        scores = []
        for color in colors[:5]:
            distances = [np.linalg.norm(color - urban_color) for urban_color in urban_colors]
            min_distance = min(distances)
            score = max(0, 1 - min_distance / 255)
            scores.append(score)
        
        return np.mean(scores)
    
    def _generate_color_scheme_from_best_colors(self, best_scheme_colors):
        """基于最佳方案的颜色生成建筑配色方案"""
        try:
            if not best_scheme_colors or len(best_scheme_colors) == 0:
                return self.default_colors.copy()
            
            # 确保颜色格式正确
            colors = np.array(best_scheme_colors)
            if colors.max() > 1:
                colors = colors / 255.0
            
            # 选择主要颜色（前8个）
            main_colors = colors[:8] if colors.shape[0] >= 8 else colors
            
            # 生成基于主要颜色的建筑配色方案
            generated_colors = {}
            color_keys = ['facade', 'environment', 'material', 'seasonal', 'lighting', 'urban', 'sustainability', 'performance']
            
            for i, color_key in enumerate(color_keys):
                if i < main_colors.shape[0]:
                    # 转换为十六进制
                    hex_color = '#{:02x}{:02x}{:02x}'.format(
                        int(main_colors[i][0] * 255),
                        int(main_colors[i][1] * 255),
                        int(main_colors[i][2] * 255)
                    )
                    generated_colors[color_key] = hex_color
                else:
                    generated_colors[color_key] = self.default_colors[color_key]
            
            return generated_colors
            
        except Exception as e:
            self.logger.warning(f"生成建筑配色方案失败，使用默认方案: {str(e)}")
            return self.default_colors.copy()
    
    def _calculate_sustainability_score(self, colors):
        """计算可持续性评分"""
        # 基于颜色的环保特性（低能耗、天然材料等）
        if isinstance(colors, list):
            colors = np.array(colors)
        
        # 计算颜色的饱和度（低饱和度更环保）
        hsv_colors = []
        for color in colors:
            r, g, b = color / 255.0
            max_val = max(r, g, b)
            min_val = min(r, g, b)
            saturation = (max_val - min_val) / max_val if max_val > 0 else 0
            hsv_colors.append(saturation)
        
        avg_saturation = np.mean(hsv_colors)
        # 低饱和度得分更高
        return 1 - avg_saturation  
  
    def _add_subplot_label(self, ax, label, x=-0.1, y=1.05):
        """添加子图标签"""
        ax.text(x, y, f'({label})', transform=ax.transAxes,
               fontsize=12, fontweight='bold', va='bottom', ha='right')
    
    def _plot_facade_distribution(self, ax, architectural_data):
        """绘制建筑立面色彩分布分析"""
        facade_colors = architectural_data['facade_colors'][0]  # 使用第一个方案的颜色
        
        if isinstance(facade_colors, list):
            facade_colors = np.array(facade_colors)
        
        # 创建颜色分布图
        n_colors = min(8, len(facade_colors))
        
        # 绘制颜色块
        for i, color in enumerate(facade_colors[:n_colors]):
            # 归一化颜色
            if color.max() > 1:
                color_norm = color / 255.0
            else:
                color_norm = color
            
            # 绘制颜色块
            rect = Rectangle((i * 1.2, 0), 1, 1, facecolor=color_norm, 
                           edgecolor='black', linewidth=1.5)
            ax.add_patch(rect)
            
            # 添加RGB标签
            ax.text(i * 1.2 + 0.5, -0.3, f'RGB\n({int(color[0])},{int(color[1])},{int(color[2])})',
                   ha='center', va='top', fontsize=8)
        
        ax.set_xlim(-0.5, n_colors * 1.2)
        ax.set_ylim(-0.5, 1.5)
        ax.set_title('Facade Color Distribution Analysis', fontweight='bold', fontsize=12)
        ax.axis('off')
    
    def _plot_environmental_adaptation(self, ax, architectural_data):
        """绘制环境适应性评估"""
        env_scores = architectural_data['environmental_adaptation']
        
        # 创建条形图
        x_pos = np.arange(len(env_scores))
        bars = ax.bar(x_pos, env_scores, color=self.colors['environment'], 
                     alpha=0.7, edgecolor='black', linewidth=1)
        
        # 添加数值标签
        for bar, score in zip(bars, env_scores):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{score:.3f}', ha='center', va='bottom', fontweight='bold')
        
        ax.set_xlabel('Schemes')
        ax.set_ylabel('Environmental Score')
        ax.set_title('Environmental Adaptation Assessment', fontweight='bold')
        ax.set_xticks(x_pos)
        ax.set_xticklabels([f'S{i+1}' for i in range(len(env_scores))])
        ax.grid(True, alpha=0.3)
        ax.set_ylim(0, 1)
    
    def _plot_material_compatibility(self, ax, architectural_data):
        """绘制材质兼容性分析"""
        material_scores = architectural_data['material_compatibility']
        
        # 创建饼图
        labels = [f'Scheme {i+1}' for i in range(len(material_scores))]
        colors = plt.cm.Set3(np.linspace(0, 1, len(material_scores)))
        
        wedges, texts, autotexts = ax.pie(material_scores, labels=labels, colors=colors,
                                         autopct='%1.2f', startangle=90)
        
        ax.set_title('Material Compatibility Analysis', fontweight='bold')
    
    def _plot_seasonal_variation(self, ax, architectural_data):
        """绘制季节性色彩变化模拟"""
        seasonal_scores = architectural_data['seasonal_variation']
        
        # 创建雷达图
        angles = np.linspace(0, 2 * np.pi, len(seasonal_scores), endpoint=False)
        seasonal_scores_plot = seasonal_scores + [seasonal_scores[0]]  # 闭合图形
        angles_plot = np.concatenate([angles, [angles[0]]])
        
        ax.plot(angles_plot, seasonal_scores_plot, 'o-', linewidth=2, 
               color=self.colors['seasonal'], markersize=6)
        ax.fill(angles_plot, seasonal_scores_plot, alpha=0.25, color=self.colors['seasonal'])
        
        ax.set_xticks(angles)
        ax.set_xticklabels([f'S{i+1}' for i in range(len(seasonal_scores))])
        ax.set_ylim(0, 1)
        ax.set_title('Seasonal Variation Simulation', fontweight='bold')
        ax.grid(True, alpha=0.3)
    
    def _plot_lighting_performance(self, ax, architectural_data):
        """绘制光照条件下的色彩表现"""
        lighting_scores = architectural_data['lighting_performance']
        
        # 创建散点图
        x_pos = np.arange(len(lighting_scores))
        scatter = ax.scatter(x_pos, lighting_scores, c=lighting_scores, 
                           cmap='YlOrRd', s=100, alpha=0.7, edgecolors='black')
        
        # 添加趋势线
        z = np.polyfit(x_pos, lighting_scores, 1)
        p = np.poly1d(z)
        ax.plot(x_pos, p(x_pos), "r--", alpha=0.8, linewidth=2)
        
        ax.set_xlabel('Schemes')
        ax.set_ylabel('Lighting Performance')
        ax.set_title('Lighting Performance Analysis', fontweight='bold')
        ax.set_xticks(x_pos)
        ax.set_xticklabels([f'S{i+1}' for i in range(len(lighting_scores))])
        ax.grid(True, alpha=0.3)
        
        # 添加颜色条
        plt.colorbar(scatter, ax=ax, shrink=0.8, label='Performance Score')
    
    def _plot_urban_integration(self, ax, architectural_data):
        """绘制城市景观融合度评估"""
        urban_scores = architectural_data['urban_integration']
        
        # 创建箱线图
        bp = ax.boxplot([urban_scores], patch_artist=True, labels=['Urban Integration'])
        bp['boxes'][0].set_facecolor(self.colors['urban'])
        bp['boxes'][0].set_alpha(0.7)
        
        # 添加散点
        y_pos = np.random.normal(1, 0.04, len(urban_scores))
        ax.scatter(y_pos, urban_scores, alpha=0.6, color='red', s=30)
        
        ax.set_ylabel('Integration Score')
        ax.set_title('Urban Integration Assessment', fontweight='bold')
        ax.grid(True, alpha=0.3)
    
    def _plot_sustainability_metrics(self, ax, architectural_data):
        """绘制建筑色彩可持续性指标"""
        sustainability_scores = architectural_data['sustainability_score']
        
        # 创建水平条形图
        y_pos = np.arange(len(sustainability_scores))
        bars = ax.barh(y_pos, sustainability_scores, color=self.colors['sustainability'], 
                      alpha=0.7, edgecolor='black', linewidth=1)
        
        # 添加数值标签
        for bar, score in zip(bars, sustainability_scores):
            width = bar.get_width()
            ax.text(width + 0.01, bar.get_y() + bar.get_height()/2.,
                   f'{score:.3f}', ha='left', va='center', fontweight='bold')
        
        ax.set_yticks(y_pos)
        ax.set_yticklabels([f'Scheme {i+1}' for i in range(len(sustainability_scores))])
        ax.set_xlabel('Sustainability Score')
        ax.set_title('Sustainability Metrics', fontweight='bold')
        ax.grid(True, alpha=0.3, axis='x')
        ax.set_xlim(0, 1)
    
    def _plot_comprehensive_performance(self, ax, architectural_data):
        """绘制综合建筑性能对比"""
        # 准备数据
        metrics_data = []
        for i in range(len(architectural_data['environmental_adaptation'])):
            row = [
                architectural_data['environmental_adaptation'][i],
                architectural_data['material_compatibility'][i],
                architectural_data['seasonal_variation'][i],
                architectural_data['lighting_performance'][i],
                architectural_data['urban_integration'][i],
                architectural_data['sustainability_score'][i]
            ]
            metrics_data.append(row)
        
        metrics_data = np.array(metrics_data)
        
        # 创建堆叠条形图
        n_schemes = len(metrics_data)
        x_pos = np.arange(n_schemes)
        
        bottom = np.zeros(n_schemes)
        colors = [self.colors['environment'], self.colors['material'], self.colors['seasonal'],
                 self.colors['lighting'], self.colors['urban'], self.colors['sustainability']]
        
        for i, (metric, color) in enumerate(zip(self.architectural_metrics, colors)):
            ax.bar(x_pos, metrics_data[:, i], bottom=bottom, label=metric, 
                  color=color, alpha=0.8, edgecolor='black', linewidth=0.5)
            bottom += metrics_data[:, i]
        
        ax.set_xlabel('Schemes')
        ax.set_ylabel('Cumulative Performance Score')
        ax.set_title('Comprehensive Architectural Performance Comparison', fontweight='bold', fontsize=14)
        ax.set_xticks(x_pos)
        ax.set_xticklabels([f'Scheme {i+1}' for i in range(n_schemes)])
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=9)
        ax.grid(True, alpha=0.3, axis='y')

def main():
    """测试建筑色彩应用分析图"""
    # 创建测试数据
    test_schemes = []
    for i in range(6):
        scheme = {
            'id': f'scheme_{i+1}',
            'colors': np.random.randint(50, 200, (8, 3)),
            'harmony_score': np.random.uniform(0.5, 0.95),
            'contrast_score': np.random.uniform(0.4, 0.9),
            'overall_score': np.random.uniform(0.5, 0.9)
        }
        test_schemes.append(scheme)
    
    # 生成图表
    generator = ArchitecturalAnalysisFixed()
    output_path = "08_architectural_analysis_fixed.png"
    
    result = generator.generate_architectural_analysis(test_schemes, output_path)
    
    if result:
        print(f"✅ 建筑色彩应用分析图生成成功: {result}")
    else:
        print("❌ 图表生成失败")

if __name__ == "__main__":
    main()