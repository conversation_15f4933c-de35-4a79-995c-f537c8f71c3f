#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据流管理器
统一管理所有图表生成器的数据传递
"""

import logging
from typing import Dict, List, Any, Optional
import numpy as np

class DataFlowManager:
    """数据流管理器 - 确保所有图表生成器接收到正确的数据"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.chart_generators = {}
        self.standardized_data = {}
    
    def register_chart_generator(self, name: str, generator: Any):
        """注册图表生成器"""
        self.chart_generators[name] = generator
        self.logger.info(f"注册图表生成器: {name}")
    
    def set_standardized_data(self, data_dict: Dict[str, Any]):
        """设置标准化数据"""
        self.standardized_data = data_dict
        self.logger.info("设置标准化数据完成")
        
        # 为所有注册的图表生成器设置数据
        self._distribute_data_to_generators()
    
    def _distribute_data_to_generators(self):
        """将数据分发给所有图表生成器"""
        for name, generator in self.chart_generators.items():
            try:
                if hasattr(generator, 'set_data'):
                    # 为不同类型的生成器准备特定数据
                    specific_data = self._prepare_specific_data(name, generator)
                    generator.set_data(specific_data)
                    self.logger.info(f"为 {name} 设置数据成功")
                else:
                    self.logger.warning(f"图表生成器 {name} 没有 set_data 方法")
            except Exception as e:
                self.logger.error(f"为 {name} 设置数据失败: {str(e)}")
    
    def _prepare_specific_data(self, generator_name: str, generator: Any) -> Dict[str, Any]:
        """为特定生成器准备数据"""
        # 基础数据（所有生成器都需要）
        specific_data = self.standardized_data.copy()
        
        # 根据生成器类型添加特定数据
        if 'chinese' in generator_name.lower():
            # 中文图表生成器特定数据
            specific_data.update(self._prepare_chinese_specific_data())
        
        elif 'architectural' in generator_name.lower():
            # 建筑分析图表特定数据
            specific_data.update(self._prepare_architectural_specific_data())
        
        elif 'optimal' in generator_name.lower():
            # 最优方案对比图表特定数据
            specific_data.update(self._prepare_optimal_specific_data())
        
        elif 'paper' in generator_name.lower():
            # 论文图表特定数据
            specific_data.update(self._prepare_paper_specific_data())
        
        return specific_data
    
    def _prepare_chinese_specific_data(self) -> Dict[str, Any]:
        """准备中文图表特定数据"""
        return {
            'chart_language': 'chinese',
            'font_family': 'SimHei',
            'title_prefix': '中文版_',
            'use_chinese_labels': True
        }
    
    def _prepare_architectural_specific_data(self) -> Dict[str, Any]:
        """准备建筑分析图表特定数据"""
        # 计算建筑特定指标
        architectural_metrics = {}
        
        if 'best_schemes' in self.standardized_data:
            schemes = self.standardized_data['best_schemes']
            architectural_metrics = {
                'environmental_adaptation': [self._calculate_environmental_score(s.get('colors', [])) for s in schemes],
                'material_compatibility': [self._calculate_material_score(s.get('colors', [])) for s in schemes],
                'seasonal_variation': [self._calculate_seasonal_score(s.get('colors', [])) for s in schemes],
                'lighting_performance': [self._calculate_lighting_score(s.get('colors', [])) for s in schemes],
                'urban_integration': [self._calculate_urban_score(s.get('colors', [])) for s in schemes],
                'sustainability_score': [self._calculate_sustainability_score(s.get('colors', [])) for s in schemes]
            }
        
        return {
            'architectural_metrics': architectural_metrics,
            'building_types': ['住宅建筑', '商业建筑', '公共建筑', '工业建筑', '历史建筑'],
            'analysis_focus': 'architectural'
        }
    
    def _prepare_optimal_specific_data(self) -> Dict[str, Any]:
        """准备最优方案对比图表特定数据"""
        # 计算推荐度评分
        recommendation_scores = []
        
        if 'best_schemes' in self.standardized_data:
            for scheme in self.standardized_data['best_schemes']:
                harmony = scheme.get('harmony_score', 0.7)
                overall = scheme.get('overall_score', 0.7)
                contrast = scheme.get('contrast_score', 0.6)
                
                recommendation = (harmony * 0.4 + overall * 0.4 + contrast * 0.2)
                recommendation_scores.append(recommendation)
        
        return {
            'recommendation_scores': recommendation_scores,
            'comparison_focus': 'optimal',
            'ranking_method': 'comprehensive'
        }
    
    def _prepare_paper_specific_data(self) -> Dict[str, Any]:
        """准备论文图表特定数据"""
        return {
            'academic_style': True,
            'high_resolution': True,
            'scientific_notation': True,
            'publication_ready': True
        }
    
    def _calculate_environmental_score(self, colors: List) -> float:
        """计算环境适应性评分"""
        if not colors or len(colors) == 0:
            return 0.7
        
        colors_array = np.array(colors)
        if colors_array.max() > 1:
            colors_array = colors_array / 255.0
        
        # 基于颜色与自然色彩的相似度
        natural_colors = np.array([[0.545, 0.271, 0.075], [0.133, 0.545, 0.133], [0.275, 0.510, 0.706]])
        
        scores = []
        for color in colors_array[:5]:
            distances = [np.linalg.norm(color - nat_color) for nat_color in natural_colors]
            min_distance = min(distances)
            score = max(0, 1 - min_distance)
            scores.append(score)
        
        return np.mean(scores) if scores else 0.7
    
    def _calculate_material_score(self, colors: List) -> float:
        """计算材质兼容性评分"""
        if not colors or len(colors) == 0:
            return 0.6
        
        colors_array = np.array(colors)
        if colors_array.max() > 1:
            colors_array = colors_array / 255.0
        
        # 基于颜色与建筑材质的匹配度
        material_colors = np.array([[0.545, 0.271, 0.075], [0.412, 0.412, 0.412], [0.824, 0.706, 0.549]])
        
        scores = []
        for color in colors_array[:5]:
            distances = [np.linalg.norm(color - mat_color) for mat_color in material_colors]
            min_distance = min(distances)
            score = max(0, 1 - min_distance)
            scores.append(score)
        
        return np.mean(scores) if scores else 0.6
    
    def _calculate_seasonal_score(self, colors: List) -> float:
        """计算季节性适应评分"""
        if not colors or len(colors) == 0:
            return 0.65
        
        colors_array = np.array(colors)
        if colors_array.max() > 1:
            colors_array = colors_array / 255.0
        
        # 计算颜色的温度感知
        warm_colors = colors_array[:, 0] > colors_array[:, 2]  # 红色 > 蓝色
        warm_ratio = np.mean(warm_colors)
        
        # 季节适应性 = 温暖色彩比例的平衡度
        return 1 - abs(warm_ratio - 0.5) * 2
    
    def _calculate_lighting_score(self, colors: List) -> float:
        """计算光照性能评分"""
        if not colors or len(colors) == 0:
            return 0.6
        
        colors_array = np.array(colors)
        if colors_array.max() > 1:
            colors_array = colors_array / 255.0
        
        # 计算平均亮度
        brightness = np.mean(colors_array, axis=1)
        avg_brightness = np.mean(brightness)
        
        # 理想亮度范围是0.4-0.7
        ideal_brightness = 0.55
        score = 1 - abs(avg_brightness - ideal_brightness) / 0.55
        
        return max(0, min(1, score))
    
    def _calculate_urban_score(self, colors: List) -> float:
        """计算城市融合度评分"""
        if not colors or len(colors) == 0:
            return 0.65
        
        colors_array = np.array(colors)
        if colors_array.max() > 1:
            colors_array = colors_array / 255.0
        
        # 基于颜色与城市环境的协调性
        urban_colors = np.array([[0.502, 0.502, 0.502], [0.412, 0.412, 0.412], [0.663, 0.663, 0.663]])
        
        scores = []
        for color in colors_array[:5]:
            distances = [np.linalg.norm(color - urban_color) for urban_color in urban_colors]
            min_distance = min(distances)
            score = max(0, 1 - min_distance)
            scores.append(score)
        
        return np.mean(scores) if scores else 0.65
    
    def _calculate_sustainability_score(self, colors: List) -> float:
        """计算可持续性评分"""
        if not colors or len(colors) == 0:
            return 0.7
        
        colors_array = np.array(colors)
        if colors_array.max() > 1:
            colors_array = colors_array / 255.0
        
        # 计算颜色的饱和度（低饱和度更环保）
        hsv_colors = []
        for color in colors_array:
            r, g, b = color
            max_val = max(r, g, b)
            min_val = min(r, g, b)
            saturation = (max_val - min_val) / max_val if max_val > 0 else 0
            hsv_colors.append(saturation)
        
        avg_saturation = np.mean(hsv_colors)
        return 1 - avg_saturation  # 低饱和度得分更高
    
    def generate_all_charts(self) -> Dict[str, Any]:
        """生成所有图表"""
        results = {}
        
        for name, generator in self.chart_generators.items():
            try:
                self.logger.info(f"开始生成图表: {name}")
                
                # 根据生成器类型调用相应的生成方法
                if hasattr(generator, 'generate_all_charts'):
                    result = generator.generate_all_charts()
                elif hasattr(generator, 'generate_enhanced_optimal_comparison'):
                    result = generator.generate_enhanced_optimal_comparison(
                        self.standardized_data.get('best_schemes', []),
                        f'./output/visualization/{name}_enhanced.png'
                    )
                elif hasattr(generator, 'generate_architectural_analysis'):
                    result = generator.generate_architectural_analysis(
                        self.standardized_data.get('best_schemes', []),
                        f'./output/visualization/{name}_analysis.png'
                    )
                else:
                    # 尝试调用默认的生成方法
                    result = self._call_default_generation_method(generator)
                
                results[name] = result
                
                if result:
                    self.logger.info(f"✅ {name} 生成成功")
                else:
                    self.logger.warning(f"⚠️ {name} 生成失败")
                    
            except Exception as e:
                self.logger.error(f"❌ {name} 生成出错: {str(e)}")
                results[name] = None
        
        return results
    
    def _call_default_generation_method(self, generator: Any) -> Any:
        """调用默认的生成方法"""
        # 尝试常见的生成方法名
        method_names = [
            'generate_clustering_results_visualization',
            'generate_color_correlation_heatmap',
            'generate_scheme_generation_statistics',
            'generate_color_network_graph',
            'generate_all_schemes_analysis'
        ]
        
        for method_name in method_names:
            if hasattr(generator, method_name):
                try:
                    method = getattr(generator, method_name)
                    return method()
                except Exception as e:
                    self.logger.warning(f"调用 {method_name} 失败: {str(e)}")
                    continue
        
        return None
    
    def get_data_summary(self) -> Dict[str, Any]:
        """获取数据摘要"""
        summary = {
            'total_generators': len(self.chart_generators),
            'registered_generators': list(self.chart_generators.keys()),
            'data_keys': list(self.standardized_data.keys()),
            'primary_colors_count': len(self.standardized_data.get('primary_colors', [])),
            'secondary_colors_count': len(self.standardized_data.get('secondary_colors', [])),
            'schemes_count': {
                'gradient': len(self.standardized_data.get('gradient_schemes', [])),
                'insertion': len(self.standardized_data.get('insertion_schemes', [])),
                'best': len(self.standardized_data.get('best_schemes', [])),
                'all': len(self.standardized_data.get('all_schemes', [])),
                'evaluated': len(self.standardized_data.get('evaluated_schemes', []))
            }
        }
        
        return summary