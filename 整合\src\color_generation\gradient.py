import os
import numpy as np
import random
import math
import sys
from PIL import Image
import json

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from src.utils import rgb_to_lab, lab_to_rgb, create_color_palette, ensure_dir

class GradientGenerator:
    """渐变生成器类"""
    
    def __init__(self, config):
        self.config = config
        self.num_steps_options = config['color_generation']['gradient']['num_steps_options']
        self.noise_strength_options = config['color_generation']['gradient']['noise_strength_options']
        self.output_dir = config['output']['color_schemes_dir']
        ensure_dir(os.path.join(self.output_dir, "gradient"))
    
    def get_user_input(self, prompt, default=None, input_type=str):
        """获取用户输入，带有默认值"""
        if default is not None:
            prompt = f"{prompt} [默认: {default}]: "
        else:
            prompt = f"{prompt}: "
        
        user_input = input(prompt).strip()
        
        if user_input == "" and default is not None:
            return default
        
        if input_type == int:
            try:
                return int(user_input)
            except ValueError:
                print("请输入有效的整数！")
                return self.get_user_input(prompt, default, input_type)
        elif input_type == float:
            try:
                return float(user_input)
            except ValueError:
                print("请输入有效的数字！")
                return self.get_user_input(prompt, default, input_type)
        elif input_type == bool:
            return user_input.lower() in ('yes', 'y', 'true', 't', '1')
        elif input_type == list:
            try:
                return json.loads(user_input)
            except json.JSONDecodeError:
                print("请输入有效的列表格式，例如: [1, 2, 3]")
                return self.get_user_input(prompt, default, input_type)
        else:
            return user_input
    
    def add_lab_noise(self, lab_values, noise_strength=1.0):
        """添加Lab空间噪声"""
        L, a, b = lab_values
        L += random.uniform(-noise_strength, noise_strength)
        a += random.uniform(-noise_strength, noise_strength)
        b += random.uniform(-noise_strength, noise_strength)
        return (L, a, b)
    
    def bezier_interpolation(self, points, steps):
        """贝塞尔曲线插值"""
        t = np.linspace(0, 1, steps)
        n = len(points)-1
        result = []
        for i in range(steps):
            sum_L = sum_a = sum_b = 0
            for k in range(n+1):
                coeff = math.comb(n, k) * (t[i]**k) * ((1-t[i])**(n-k))
                sum_L += coeff * points[k][0]
                sum_a += coeff * points[k][1]
                sum_b += coeff * points[k][2]
            result.append((sum_L, sum_a, sum_b))
        return result
    
    def generate_gradient(self, control_points, num_steps=10, noise_strength=1.0):
        """生成渐变色"""
        # 插值
        interpolated = self.bezier_interpolation(control_points, num_steps)
        
        # 添加噪声
        noisy_colors = [self.add_lab_noise(p, noise_strength) for p in interpolated]
        
        # 转换回RGB
        rgb_colors = [lab_to_rgb(*color) for color in noisy_colors]
        
        return noisy_colors, rgb_colors
    
    def generate_gradient_image(self, colors, output_path, img_width=800, img_height=100):
        """生成渐变图像"""
        img = Image.new('RGB', (img_width, img_height))
        step_size = img_width // len(colors)
        
        for i, color in enumerate(colors):
            start_x = i * step_size
            end_x = (i+1) * step_size if i < len(colors)-1 else img_width
            for x in range(start_x, end_x):
                for y in range(img_height):
                    img.putpixel((x, y), tuple(map(int, color)))
        
        img.save(output_path)
        return output_path
    
    def process(self, top_colors):
        """处理渐变生成
        
        参数:
            top_colors: 相关度最高的三个颜色
            
        返回:
            生成的渐变方案列表
        """
        print("开始生成渐变方案...")
        
        # 显示选中的颜色
        print("\n选中的颜色:")
        for i, color in enumerate(top_colors):
            print(f"{i+1}. RGB{tuple(map(int, color))}")
        
        # 询问是否修改色块数量选项
        modify_steps = self.get_user_input("\n是否修改色块数量选项? (y/n)", "n")
        if modify_steps.lower() in ('yes', 'y', 'true', 't', '1'):
            print(f"当前色块数量选项: {self.num_steps_options}")
            self.num_steps_options = self.get_user_input("请输入新的色块数量选项列表 (例如: [2, 4, 6, 8, 10])", 
                                                        self.num_steps_options, list)
        
        # 询问是否修改噪声系数选项
        modify_noise = self.get_user_input("是否修改噪声系数选项? (y/n)", "n")
        if modify_noise.lower() in ('yes', 'y', 'true', 't', '1'):
            print(f"当前噪声系数选项: {self.noise_strength_options}")
            self.noise_strength_options = self.get_user_input("请输入新的噪声系数选项列表 (例如: [1, 3, 5, 7, 9])", 
                                                             self.noise_strength_options, list)
        
        # 将RGB颜色转换为Lab
        control_points = [rgb_to_lab(*color) for color in top_colors]
        
        # 生成所有组合方案
        gradient_schemes = []
        scheme_count = 0
        
        # 询问是否生成所有组合
        generate_all = self.get_user_input("是否生成所有色块数量和噪声系数的组合? (y/n)", "y")
        
        if generate_all.lower() in ('yes', 'y', 'true', 't', '1'):
            # 生成所有组合
            for num_steps in self.num_steps_options:
                for noise_strength in self.noise_strength_options:
                    scheme_count += 1
                    
                    # 生成渐变颜色
                    noisy_lab, gradient_colors = self.generate_gradient(
                        control_points, num_steps=num_steps, noise_strength=noise_strength)
                    
                    # 保存结果
                    output_path = os.path.join(
                        self.output_dir, "gradient", 
                        f"gradient_s{num_steps}_n{noise_strength}_{scheme_count}.png")
                    
                    self.generate_gradient_image(gradient_colors, output_path)
                    
                    # 保存颜色信息
                    info_path = os.path.join(
                        self.output_dir, "gradient", 
                        f"gradient_info_s{num_steps}_n{noise_strength}_{scheme_count}.txt")
                    
                    with open(info_path, 'w') as f:
                        f.write(f"参数组合:\n  色块数量: {num_steps}\n  噪声系数: {noise_strength}\n\n")
                        for j in range(len(gradient_colors)):
                            lab_values = tuple(map(lambda x: round(x, 2), noisy_lab[j]))
                            rgb_values = tuple(map(int, gradient_colors[j]))
                            f.write(f"色块{j+1}:\n  Lab: {lab_values}\n  RGB: {rgb_values}\n\n")
                    
                    # 创建颜色比例图
                    palette_path = os.path.join(
                        self.output_dir, "gradient", 
                        f"gradient_palette_s{num_steps}_n{noise_strength}_{scheme_count}.png")
                    
                    proportions = [1.0/len(gradient_colors)] * len(gradient_colors)
                    create_color_palette(
                        gradient_colors, proportions, palette_path, 
                        title=f"渐变方案 {scheme_count} (步数={num_steps}, 噪声={noise_strength})")
                    
                    # 添加到方案列表
                    gradient_schemes.append({
                        'scheme_id': scheme_count,
                        'num_steps': num_steps,
                        'noise_strength': noise_strength,
                        'colors': gradient_colors,
                        'image_path': output_path,
                        'info_path': info_path,
                        'palette_path': palette_path
                    })
                    
                    print(f"生成渐变方案 {scheme_count}: 色块数量={num_steps}, 噪声系数={noise_strength}")
        else:
            # 手动选择组合
            custom_count = self.get_user_input("请输入要生成的方案数量", 5, int)
            
            for i in range(custom_count):
                scheme_count += 1
                
                # 用户选择参数
                print(f"\n方案 {scheme_count} 参数设置:")
                print(f"可选色块数量: {self.num_steps_options}")
                num_steps = self.get_user_input("请选择色块数量", self.num_steps_options[0], int)
                
                print(f"可选噪声系数: {self.noise_strength_options}")
                noise_strength = self.get_user_input("请选择噪声系数", self.noise_strength_options[0], float)
                
                # 生成渐变颜色
                noisy_lab, gradient_colors = self.generate_gradient(
                    control_points, num_steps=num_steps, noise_strength=noise_strength)
                
                # 保存结果
                output_path = os.path.join(
                    self.output_dir, "gradient", 
                    f"gradient_s{num_steps}_n{noise_strength}_{scheme_count}.png")
                
                self.generate_gradient_image(gradient_colors, output_path)
                
                # 保存颜色信息
                info_path = os.path.join(
                    self.output_dir, "gradient", 
                    f"gradient_info_s{num_steps}_n{noise_strength}_{scheme_count}.txt")
                
                with open(info_path, 'w') as f:
                    f.write(f"参数组合:\n  色块数量: {num_steps}\n  噪声系数: {noise_strength}\n\n")
                    for j in range(len(gradient_colors)):
                        lab_values = tuple(map(lambda x: round(x, 2), noisy_lab[j]))
                        rgb_values = tuple(map(int, gradient_colors[j]))
                        f.write(f"色块{j+1}:\n  Lab: {lab_values}\n  RGB: {rgb_values}\n\n")
                
                # 创建颜色比例图
                palette_path = os.path.join(
                    self.output_dir, "gradient", 
                    f"gradient_palette_s{num_steps}_n{noise_strength}_{scheme_count}.png")
                
                proportions = [1.0/len(gradient_colors)] * len(gradient_colors)
                create_color_palette(
                    gradient_colors, proportions, palette_path, 
                    title=f"渐变方案 {scheme_count} (步数={num_steps}, 噪声={noise_strength})")
                
                # 添加到方案列表
                gradient_schemes.append({
                    'scheme_id': scheme_count,
                    'num_steps': num_steps,
                    'noise_strength': noise_strength,
                    'colors': gradient_colors,
                    'image_path': output_path,
                    'info_path': info_path,
                    'palette_path': palette_path
                })
                
                print(f"生成渐变方案 {scheme_count}: 色块数量={num_steps}, 噪声系数={noise_strength}")
        
        # 保存所有方案信息
        with open(os.path.join(self.output_dir, "gradient", "all_gradient_schemes.json"), 'w') as f:
            json.dump([{
                'scheme_id': s['scheme_id'],
                'num_steps': s['num_steps'],
                'noise_strength': s['noise_strength'],
                'colors': [list(map(int, c)) for c in s['colors']],
                'image_path': s['image_path'],
                'info_path': s['info_path'],
                'palette_path': s['palette_path']
            } for s in gradient_schemes], f, indent=2)
        
        print(f"渐变方案生成完成，共 {len(gradient_schemes)} 个方案")
        return gradient_schemes 