import os
import numpy as np
import random
import sys
from PIL import Image, ImageDraw
import json

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from src.utils import create_color_palette, ensure_dir

class ColorInsertion:
    """颜色插入类"""
    
    def __init__(self, config):
        self.config = config
        self.primary_secondary_ratio = config['color_generation']['primary_secondary_ratio']
        # 不再使用固定的比例方案
        self.output_dir = config['output']['color_schemes_dir']
        ensure_dir(os.path.join(self.output_dir, "insertion"))
    
    def get_user_input(self, prompt, default=None, input_type=str):
        """获取用户输入，带有默认值"""
        if default is not None:
            prompt = f"{prompt} [默认: {default}]: "
        else:
            prompt = f"{prompt}: "
        
        user_input = input(prompt).strip()
        
        if user_input == "" and default is not None:
            return default
        
        if input_type == int:
            try:
                return int(user_input)
            except ValueError:
                print("请输入有效的整数！")
                return self.get_user_input(prompt, default, input_type)
        elif input_type == float:
            try:
                return float(user_input)
            except ValueError:
                print("请输入有效的数字！")
                return self.get_user_input(prompt, default, input_type)
        elif input_type == bool:
            return user_input.lower() in ('yes', 'y', 'true', 't', '1')
        elif input_type == list:
            try:
                return json.loads(user_input)
            except json.JSONDecodeError:
                print("请输入有效的列表格式，例如: [1, 2, 3]")
                return self.get_user_input(prompt, default, input_type)
        else:
            return user_input
    
    def generate_random_proportions(self, num_colors=4, total_proportion=20):
        """生成随机的颜色比例（总和为指定值）"""
        # 生成随机权重
        weights = [random.uniform(1, 10) for _ in range(num_colors)]
        total_weight = sum(weights)
        
        # 按权重分配总比例
        proportions = [weight / total_weight * total_proportion for weight in weights]
        
        # 四舍五入到一位小数
        proportions = [round(p, 1) for p in proportions]
        
        # 调整以确保总和为指定值
        diff = total_proportion - sum(proportions)
        proportions[0] += diff  # 将差值添加到第一个比例
        
        return proportions
    
    def apply_color_blocks(self, base_colors, insert_colors, proportions):
        """应用颜色块 - 随机位置插入
        
        参数:
            base_colors: 基础颜色列表（渐变方案）
            insert_colors: 插入颜色列表（二层数据颜色）
            proportions: 插入比例列表（总和为20%）
            
        返回:
            合成后的颜色列表和比例列表
        """
        import random
        
        # 计算基础颜色的比例（总和为80%）
        base_proportions = [(1 - sum(proportions)/100) / len(base_colors)] * len(base_colors)
        
        # 创建基础颜色和比例的配对列表
        base_pairs = list(zip(base_colors, base_proportions))
        
        # 创建插入颜色和比例的配对列表
        insert_pairs = [(color, prop/100) for color, prop in zip(insert_colors, proportions)]
        
        # 随机选择插入位置
        combined_pairs = base_pairs.copy()
        
        # 为每个插入颜色随机选择位置
        for insert_pair in insert_pairs:
            # 随机选择插入位置（可以在任何位置，包括开头、中间、末尾）
            insert_position = random.randint(0, len(combined_pairs))
            combined_pairs.insert(insert_position, insert_pair)
        
        # 分离颜色和比例
        combined_colors = [pair[0] for pair in combined_pairs]
        combined_proportions = [pair[1] for pair in combined_pairs]
        
        return combined_colors, combined_proportions
    
    def generate_composite_image(self, colors, proportions, output_path, width=800, height=100):
        """生成合成图像"""
        img = Image.new('RGB', (width, height))
        draw = ImageDraw.Draw(img)
        
        # 绘制颜色块
        x_pos = 0
        for color, prop in zip(colors, proportions):
            block_width = int(width * prop)
            if block_width > 0:
                draw.rectangle(
                    [(x_pos, 0), (x_pos + block_width, height)], 
                    fill=tuple(map(int, color))
                )
                x_pos += block_width
        
        img.save(output_path)
        return output_path
    
    def process(self, gradient_schemes, secondary_colors):
        """处理颜色插入
        
        参数:
            gradient_schemes: 渐变方案列表
            secondary_colors: 二层数据颜色列表
            
        返回:
            合成后的方案列表
        """
        print("开始插入二层数据颜色...")
        
        # 显示二层数据颜色
        print("\n二层数据颜色:")
        for i, color in enumerate(secondary_colors[:10]):  # 只显示前10个
            print(f"{i+1}. RGB{tuple(map(int, color))}")
        
        # 询问是否修改一层与二层数据比例
        modify_ratio = self.get_user_input("\n是否修改一层与二层数据比例? (y/n)", "n")
        if modify_ratio.lower() in ('yes', 'y', 'true', 't', '1'):
            print(f"当前一层与二层数据比例: {self.primary_secondary_ratio}")
            self.primary_secondary_ratio = self.get_user_input("请输入新的比例 (0-1之间)", 
                                                             self.primary_secondary_ratio, float)
        
        # 询问是否使用随机比例
        use_random = self.get_user_input("是否使用随机比例插入二层数据颜色? (y/n)", "y")
        use_random = use_random.lower() in ('yes', 'y', 'true', 't', '1')
        
        # 如果不使用随机比例，则询问用户输入比例方案数量
        num_schemes = 5  # 默认方案数量
        proportion_schemes = []
        
        if not use_random:
            num_schemes = self.get_user_input("请输入比例方案数量", num_schemes, int)
            print("请输入每个方案的比例（4个数字，总和为20，用逗号分隔）")
            
            for i in range(num_schemes):
                while True:
                    try:
                        scheme_str = self.get_user_input(f"方案 {i+1}")
                        scheme = [float(x.strip()) for x in scheme_str.split(',')]
                        
                        if len(scheme) != 4:
                            print("请输入4个数字！")
                            continue
                            
                        if abs(sum(scheme) - 20) > 0.001:
                            print("数字总和必须为20！")
                            continue
                            
                        proportion_schemes.append(scheme)
                        break
                    except ValueError:
                        print("请输入有效的数字列表！")
        else:
            # 生成随机比例方案
            num_schemes = self.get_user_input("请输入要生成的随机比例方案数量", num_schemes, int)
            for i in range(num_schemes):
                proportion_schemes.append(self.generate_random_proportions(4, 20))
                print(f"随机比例方案 {i+1}: {proportion_schemes[-1]}")
        
        # 选择所有二层数据颜色（应该只有4个）
        selected_secondary_colors = secondary_colors
        if len(selected_secondary_colors) < 4:
            # 如果二层数据颜色不足4个，则复制已有颜色
            while len(selected_secondary_colors) < 4:
                selected_secondary_colors.append(selected_secondary_colors[0])
        elif len(selected_secondary_colors) > 4:
            # 如果超过4个，只取前4个
            selected_secondary_colors = selected_secondary_colors[:4]
        
        # 询问是否手动选择二层数据颜色
        manual_select = self.get_user_input("是否手动选择二层数据颜色? (y/n)", "n")
        if manual_select.lower() in ('yes', 'y', 'true', 't', '1'):
            selected_secondary_colors = []
            for i in range(4):
                while True:
                    idx = self.get_user_input(f"请选择第 {i+1} 个颜色 (输入序号1-{len(secondary_colors)})", i+1, int) - 1
                    if 0 <= idx < len(secondary_colors):
                        selected_secondary_colors.append(secondary_colors[idx])
                        print(f"已选择颜色: RGB{tuple(map(int, secondary_colors[idx]))}")
                        break
                    else:
                        print("无效的颜色序号！")
        
        # 询问是否选择特定的渐变方案
        select_gradients = self.get_user_input("是否只选择部分渐变方案进行插入? (y/n)", "n")
        selected_gradients = gradient_schemes
        
        if select_gradients.lower() in ('yes', 'y', 'true', 't', '1'):
            print("\n可用的渐变方案:")
            for i, scheme in enumerate(gradient_schemes):
                print(f"{i+1}. 方案 {scheme['scheme_id']}: 色块数量={scheme['num_steps']}, 噪声系数={scheme['noise_strength']}")
            
            selected_indices = []
            num_selected = self.get_user_input("请输入要选择的渐变方案数量", min(5, len(gradient_schemes)), int)
            
            for i in range(num_selected):
                while True:
                    idx = self.get_user_input(f"请选择第 {i+1} 个渐变方案 (输入序号1-{len(gradient_schemes)})", i+1, int) - 1
                    if 0 <= idx < len(gradient_schemes):
                        selected_indices.append(idx)
                        print(f"已选择方案 {gradient_schemes[idx]['scheme_id']}")
                        break
                    else:
                        print("无效的方案序号！")
            
            selected_gradients = [gradient_schemes[i] for i in selected_indices]
        
        # 生成所有组合方案
        insertion_schemes = []
        scheme_count = 0
        
        # 对每个渐变方案应用所有比例方案
        for gradient_scheme in selected_gradients:
            base_colors = gradient_scheme['colors']
            
            # 询问是否对当前渐变方案应用所有比例方案
            apply_all = self.get_user_input(f"\n是否对渐变方案 {gradient_scheme['scheme_id']} 应用所有比例方案? (y/n)", "y")
            
            if apply_all.lower() in ('yes', 'y', 'true', 't', '1'):
                # 应用所有比例方案
                for prop_scheme_idx, proportions in enumerate(proportion_schemes):
                    scheme_count += 1
                    
                    # 应用颜色块
                    combined_colors, combined_proportions = self.apply_color_blocks(
                        base_colors, selected_secondary_colors, proportions)
                    
                    # 保存结果
                    output_path = os.path.join(
                        self.output_dir, "insertion", 
                        f"insertion_g{gradient_scheme['scheme_id']}_p{prop_scheme_idx+1}_{scheme_count}.png")
                    
                    self.generate_composite_image(combined_colors, combined_proportions, output_path)
                    
                    # 创建颜色比例图
                    palette_path = os.path.join(
                        self.output_dir, "insertion", 
                        f"insertion_palette_g{gradient_scheme['scheme_id']}_p{prop_scheme_idx+1}_{scheme_count}.png")
                    
                    create_color_palette(
                        combined_colors, combined_proportions, palette_path, 
                        title=f"插入方案 {scheme_count} (渐变={gradient_scheme['scheme_id']}, 比例方案={prop_scheme_idx+1})")
                    
                    # 保存颜色信息
                    info_path = os.path.join(
                        self.output_dir, "insertion", 
                        f"insertion_info_g{gradient_scheme['scheme_id']}_p{prop_scheme_idx+1}_{scheme_count}.txt")
                    
                    with open(info_path, 'w') as f:
                        f.write(f"插入方案 {scheme_count}:\n")
                        f.write(f"  基础渐变方案: {gradient_scheme['scheme_id']}\n")
                        f.write(f"  比例方案: {prop_scheme_idx+1} {proportions}\n\n")
                        
                        f.write("一层数据颜色 (渐变):\n")
                        for i, color in enumerate(base_colors):
                            f.write(f"  颜色 {i+1}: RGB{tuple(map(int, color))}\n")
                        
                        f.write("\n二层数据颜色 (插入):\n")
                        for i, color in enumerate(selected_secondary_colors):
                            f.write(f"  颜色 {i+1}: RGB{tuple(map(int, color))} - 比例: {proportions[i]}%\n")
                        
                        f.write("\n合成后颜色比例:\n")
                        for i, (color, prop) in enumerate(zip(combined_colors, combined_proportions)):
                            f.write(f"  颜色 {i+1}: RGB{tuple(map(int, color))} - 比例: {prop:.2%}\n")
                    
                    # 添加到方案列表
                    insertion_schemes.append({
                        'scheme_id': scheme_count,
                        'gradient_scheme_id': gradient_scheme['scheme_id'],
                        'proportion_scheme_id': prop_scheme_idx + 1,
                        'proportions': proportions,
                        'colors': combined_colors,
                        'color_proportions': combined_proportions,
                        'image_path': output_path,
                        'info_path': info_path,
                        'palette_path': palette_path
                    })
                    
                    print(f"生成插入方案 {scheme_count}: 渐变={gradient_scheme['scheme_id']}, 比例方案={prop_scheme_idx+1}")
            else:
                # 手动选择比例方案
                print("\n可用的比例方案:")
                for i, scheme in enumerate(proportion_schemes):
                    print(f"{i+1}. {scheme}")
                
                selected_props = []
                num_selected = self.get_user_input("请输入要选择的比例方案数量", min(3, len(proportion_schemes)), int)
                
                for i in range(num_selected):
                    while True:
                        idx = self.get_user_input(f"请选择第 {i+1} 个比例方案 (输入序号1-{len(proportion_schemes)})", i+1, int) - 1
                        if 0 <= idx < len(proportion_schemes):
                            selected_props.append((idx, proportion_schemes[idx]))
                            print(f"已选择比例方案: {proportion_schemes[idx]}")
                            break
                        else:
                            print("无效的方案序号！")
                
                # 应用选中的比例方案
                for prop_scheme_idx, proportions in selected_props:
                    scheme_count += 1
                    
                    # 应用颜色块
                    combined_colors, combined_proportions = self.apply_color_blocks(
                        base_colors, selected_secondary_colors, proportions)
                    
                    # 保存结果
                    output_path = os.path.join(
                        self.output_dir, "insertion", 
                        f"insertion_g{gradient_scheme['scheme_id']}_p{prop_scheme_idx+1}_{scheme_count}.png")
                    
                    self.generate_composite_image(combined_colors, combined_proportions, output_path)
                    
                    # 创建颜色比例图
                    palette_path = os.path.join(
                        self.output_dir, "insertion", 
                        f"insertion_palette_g{gradient_scheme['scheme_id']}_p{prop_scheme_idx+1}_{scheme_count}.png")
                    
                    create_color_palette(
                        combined_colors, combined_proportions, palette_path, 
                        title=f"插入方案 {scheme_count} (渐变={gradient_scheme['scheme_id']}, 比例方案={prop_scheme_idx+1})")
                    
                    # 保存颜色信息
                    info_path = os.path.join(
                        self.output_dir, "insertion", 
                        f"insertion_info_g{gradient_scheme['scheme_id']}_p{prop_scheme_idx+1}_{scheme_count}.txt")
                    
                    with open(info_path, 'w') as f:
                        f.write(f"插入方案 {scheme_count}:\n")
                        f.write(f"  基础渐变方案: {gradient_scheme['scheme_id']}\n")
                        f.write(f"  比例方案: {prop_scheme_idx+1} {proportions}\n\n")
                        
                        f.write("一层数据颜色 (渐变):\n")
                        for i, color in enumerate(base_colors):
                            f.write(f"  颜色 {i+1}: RGB{tuple(map(int, color))}\n")
                        
                        f.write("\n二层数据颜色 (插入):\n")
                        for i, color in enumerate(selected_secondary_colors):
                            f.write(f"  颜色 {i+1}: RGB{tuple(map(int, color))} - 比例: {proportions[i]}%\n")
                        
                        f.write("\n合成后颜色比例:\n")
                        for i, (color, prop) in enumerate(zip(combined_colors, combined_proportions)):
                            f.write(f"  颜色 {i+1}: RGB{tuple(map(int, color))} - 比例: {prop:.2%}\n")
                    
                    # 添加到方案列表
                    insertion_schemes.append({
                        'scheme_id': scheme_count,
                        'gradient_scheme_id': gradient_scheme['scheme_id'],
                        'proportion_scheme_id': prop_scheme_idx + 1,
                        'proportions': proportions,
                        'colors': combined_colors,
                        'color_proportions': combined_proportions,
                        'image_path': output_path,
                        'info_path': info_path,
                        'palette_path': palette_path
                    })
                    
                    print(f"生成插入方案 {scheme_count}: 渐变={gradient_scheme['scheme_id']}, 比例方案={prop_scheme_idx+1}")
        
        # 保存所有方案信息
        with open(os.path.join(self.output_dir, "insertion", "all_insertion_schemes.json"), 'w') as f:
            json.dump([{
                'scheme_id': s['scheme_id'],
                'gradient_scheme_id': s['gradient_scheme_id'],
                'proportion_scheme_id': s['proportion_scheme_id'],
                'proportions': s['proportions'],
                'colors': [list(map(int, c)) for c in s['colors']],
                'color_proportions': s['color_proportions'],
                'image_path': s['image_path'],
                'info_path': s['info_path'],
                'palette_path': s['palette_path']
            } for s in insertion_schemes], f, indent=2)
        
        print(f"颜色插入完成，共 {len(insertion_schemes)} 个方案")
        return insertion_schemes

class InsertionGenerator(ColorInsertion):
    """
    插入生成器 - ColorInsertion的别名类
    为了保持与main.py的兼容性
    """

    def __init__(self, config):
        """初始化插入生成器"""
        super().__init__(config)

    def generate_schemes(self, gradient_schemes, secondary_colors):
        """
        生成插入色彩方案 - process方法的别名

        Args:
            gradient_schemes: 渐变方案列表
            secondary_colors: 次要立面颜色列表

        Returns:
            list: 插入色彩方案列表
        """
        return self.process(gradient_schemes, secondary_colors)