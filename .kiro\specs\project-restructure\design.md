# Design Document

## Overview

The restructured building facade color optimization system will be organized as a modular, scalable architecture that separates concerns while maintaining efficient data flow. The system will be relocated to `D:\桌面\颜色相关度\整合\new` with a clean directory structure that supports both development and production use cases.

The design emphasizes modularity, maintainability, and extensibility while preserving the core functionality of the existing system. Key improvements include enhanced semantic segmentation outputs, English-language scientific visualizations, and robust error handling throughout the pipeline.

## Architecture

### System Architecture Overview

```mermaid
graph TB
    A[Street View Images] --> B[Image Preprocessing]
    B --> C[Semantic Segmentation]
    C --> D[Color Extraction & Clustering]
    D --> E[Multi-scale Correlation Analysis]
    E --> F[Color Scheme Generation]
    F --> G[Evaluation System]
    G --> H[Scientific Visualization]
    H --> I[Data Export]
    
    C --> J[Building Extraction]
    C --> K[Vegetation Extraction]
    C --> L[Color Block Generation]
    
    subgraph "Configuration & Logging"
        M[Config Manager]
        N[Logger System]
    end
    
    M --> B
    M --> C
    M --> D
    M --> E
    M --> F
    M --> G
    M --> H
    N --> B
    N --> C
    N --> D
    N --> E
    N --> F
    N --> G
    N --> H
```

### Directory Structure

```
D:\桌面\颜色相关度\整合\new/
├── config/
│   ├── settings.json
│   ├── segmentation_config.json
│   └── visualization_config.json
├── src/
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config_manager.py
│   │   ├── logger.py
│   │   └── base_processor.py
│   ├── data_processing/
│   │   ├── __init__.py
│   │   ├── image_preprocessor.py
│   │   ├── color_extractor.py
│   │   ├── clustering_engine.py
│   │   └── correlation_analyzer.py
│   ├── segmentation/
│   │   ├── __init__.py
│   │   ├── semantic_segmenter.py
│   │   ├── building_extractor.py
│   │   ├── vegetation_extractor.py
│   │   └── color_block_generator.py
│   ├── color_optimization/
│   │   ├── __init__.py
│   │   ├── gradient_generator.py
│   │   ├── scheme_optimizer.py
│   │   └── multi_scale_analyzer.py
│   ├── evaluation/
│   │   ├── __init__.py
│   │   ├── harmony_evaluator.py
│   │   ├── historical_coordinator.py
│   │   └── visual_comfort_assessor.py
│   ├── visualization/
│   │   ├── __init__.py
│   │   ├── scientific_charts.py
│   │   ├── color_visualizer.py
│   │   └── report_generator.py
│   ├── export/
│   │   ├── __init__.py
│   │   ├── data_exporter.py
│   │   └── format_converters.py
│   └── main.py
├── data/
│   ├── input/
│   │   ├── street_view/
│   │   └── reference_colors/
│   └── output/
│       ├── preprocessed/
│       ├── segmentation/
│       │   ├── color_blocks/
│       │   ├── buildings/
│       │   └── vegetation/
│       ├── clustering/
│       ├── correlation/
│       ├── color_schemes/
│       ├── evaluation/
│       ├── visualization/
│       └── exports/
├── tests/
│   ├── unit/
│   ├── integration/
│   └── fixtures/
├── docs/
│   ├── api/
│   ├── user_guide/
│   └── examples/
├── requirements.txt
├── setup.py
└── README.md
```

## Components and Interfaces

### Core Components

#### ConfigManager
**Purpose:** Centralized configuration management with validation and environment-specific settings.

**Interface:**
```python
class ConfigManager:
    def __init__(self, config_path: str)
    def get_config(self, section: str = None) -> Dict
    def validate_config(self) -> bool
    def update_config(self, section: str, updates: Dict) -> None
```

**Key Features:**
- JSON-based configuration with schema validation
- Environment variable override support
- Default value handling
- Configuration change notifications

#### Logger
**Purpose:** Comprehensive logging system with structured output and multiple handlers.

**Interface:**
```python
class Logger:
    def __init__(self, name: str, config: Dict)
    def info(self, message: str, extra: Dict = None) -> None
    def error(self, message: str, exception: Exception = None) -> None
    def debug(self, message: str, extra: Dict = None) -> None
    def set_level(self, level: str) -> None
```

**Key Features:**
- Structured JSON logging
- File and console handlers
- Performance metrics tracking
- Error context preservation

### Data Processing Components

#### ImagePreprocessor
**Purpose:** Standardize and enhance street view images for analysis.

**Interface:**
```python
class ImagePreprocessor:
    def __init__(self, config: Dict)
    def preprocess_image(self, image_path: str) -> np.ndarray
    def batch_preprocess(self, image_paths: List[str]) -> List[np.ndarray]
    def remove_shadows(self, image: np.ndarray) -> np.ndarray
    def normalize_illumination(self, image: np.ndarray) -> np.ndarray
```

**Processing Pipeline:**
1. Shadow detection and removal using morphological operations
2. Illumination normalization using histogram equalization
3. Color enhancement using adaptive gamma correction
4. Noise reduction using bilateral filtering
5. Quality assessment and validation

#### ColorExtractor
**Purpose:** Extract dominant colors from preprocessed images using advanced clustering.

**Interface:**
```python
class ColorExtractor:
    def __init__(self, config: Dict)
    def extract_colors(self, image: np.ndarray, n_colors: int = 12) -> List[Color]
    def extract_facade_colors(self, image: np.ndarray, mask: np.ndarray) -> List[Color]
    def get_color_statistics(self, colors: List[Color]) -> Dict
```

**Key Features:**
- K-means clustering with multiple color spaces (RGB, HSV, LAB)
- Adaptive cluster number selection using elbow method
- Perceptually uniform color distance calculations
- Color significance weighting based on area coverage

#### CorrelationAnalyzer
**Purpose:** Analyze color relationships across multiple spatial scales.

**Interface:**
```python
class CorrelationAnalyzer:
    def __init__(self, config: Dict)
    def analyze_correlations(self, colors: List[Color], spatial_data: Dict) -> Dict
    def build_adjacency_matrix(self, colors: List[Color]) -> np.ndarray
    def calculate_multi_scale_influence(self, colors: List[Color]) -> Dict
```

**Analysis Methods:**
- Spatial adjacency analysis using Delaunay triangulation
- Color co-occurrence matrix generation
- Multi-scale influence calculation with distance decay
- Statistical correlation analysis using Pearson coefficients

### Segmentation Components

#### SemanticSegmenter
**Purpose:** Perform semantic segmentation using PSPNet with ADE20K dataset.

**Interface:**
```python
class SemanticSegmenter:
    def __init__(self, config: Dict)
    def segment_image(self, image: np.ndarray) -> np.ndarray
    def batch_segment(self, images: List[np.ndarray]) -> List[np.ndarray]
    def get_class_probabilities(self, image: np.ndarray) -> Dict
```

**Key Features:**
- PSPNet model with ResNet101 backbone
- ADE20K pretrained weights
- GPU acceleration support
- Batch processing optimization

#### BuildingExtractor
**Purpose:** Extract building regions with transparent backgrounds and multiple output formats.

**Interface:**
```python
class BuildingExtractor:
    def __init__(self, config: Dict)
    def extract_buildings(self, image: np.ndarray, segmentation_mask: np.ndarray) -> Dict
    def create_transparent_background(self, image: np.ndarray, mask: np.ndarray) -> np.ndarray
    def create_color_block_image(self, segmentation_mask: np.ndarray) -> np.ndarray
```

**Output Formats:**
1. **Color Block Image:** Segmented regions with distinct colors for each class
2. **Building Isolated (Transparent):** Buildings extracted with transparent background (PNG)
3. **Building Isolated (White):** Buildings extracted with white background (JPG)
4. **Building Contour:** Building outlines on black background

#### VegetationExtractor
**Purpose:** Extract vegetation regions for environmental analysis.

**Interface:**
```python
class VegetationExtractor:
    def __init__(self, config: Dict)
    def extract_vegetation(self, image: np.ndarray, segmentation_mask: np.ndarray) -> Dict
    def classify_vegetation_types(self, mask: np.ndarray) -> Dict
    def calculate_vegetation_coverage(self, mask: np.ndarray) -> float
```

**Vegetation Classes:**
- Trees (deciduous and evergreen)
- Grass and ground cover
- Shrubs and bushes
- Flowers and ornamental plants

### Color Optimization Components

#### GradientGenerator
**Purpose:** Generate smooth color transitions using Bézier curves and perceptual color spaces.

**Interface:**
```python
class GradientGenerator:
    def __init__(self, config: Dict)
    def generate_gradient(self, start_color: Color, end_color: Color, steps: int) -> List[Color]
    def create_bezier_gradient(self, control_points: List[Color], steps: int) -> List[Color]
    def optimize_gradient_smoothness(self, gradient: List[Color]) -> List[Color]
```

**Key Features:**
- Bézier curve-based color interpolation
- Perceptually uniform color transitions in LAB space
- Gradient smoothness optimization
- Multiple interpolation methods (linear, cubic, spline)

#### SchemeOptimizer
**Purpose:** Generate and optimize color schemes using multi-objective optimization.

**Interface:**
```python
class SchemeOptimizer:
    def __init__(self, config: Dict)
    def optimize_scheme(self, base_colors: List[Color], constraints: Dict) -> ColorScheme
    def evaluate_harmony(self, scheme: ColorScheme) -> float
    def apply_historical_constraints(self, scheme: ColorScheme, historical_colors: List[Color]) -> ColorScheme
```

**Optimization Objectives:**
- Color harmony maximization using color theory principles
- Historical coordination with existing architectural colors
- Visual comfort optimization for human perception
- Multi-scale consistency across viewing distances

### Evaluation Components

#### HarmonyEvaluator
**Purpose:** Assess color harmony using established color theory principles.

**Interface:**
```python
class HarmonyEvaluator:
    def __init__(self, config: Dict)
    def evaluate_harmony(self, colors: List[Color]) -> Dict
    def check_complementary_harmony(self, colors: List[Color]) -> float
    def check_analogous_harmony(self, colors: List[Color]) -> float
    def check_triadic_harmony(self, colors: List[Color]) -> float
```

**Harmony Metrics:**
- Complementary color relationships
- Analogous color progressions
- Triadic color balance
- Split-complementary arrangements
- Tetradic color schemes

#### VisualComfortAssessor
**Purpose:** Evaluate visual comfort and psychological impact of color schemes.

**Interface:**
```python
class VisualComfortAssessor:
    def __init__(self, config: Dict)
    def assess_comfort(self, scheme: ColorScheme) -> Dict
    def calculate_contrast_ratios(self, colors: List[Color]) -> List[float]
    def evaluate_saturation_balance(self, colors: List[Color]) -> float
    def assess_brightness_distribution(self, colors: List[Color]) -> Dict
```

**Comfort Metrics:**
- Contrast ratio compliance (WCAG guidelines)
- Saturation level balance
- Brightness distribution analysis
- Color temperature consistency
- Visual fatigue assessment

### Visualization Components

#### ScientificCharts
**Purpose:** Generate publication-quality scientific visualizations with English labels.

**Interface:**
```python
class ScientificCharts:
    def __init__(self, config: Dict)
    def create_clustering_visualization(self, data: Dict) -> str
    def create_correlation_heatmap(self, correlation_matrix: np.ndarray) -> str
    def create_network_graph(self, adjacency_matrix: np.ndarray) -> str
    def create_evaluation_radar(self, evaluation_data: Dict) -> str
```

**Chart Types:**
1. **Clustering Results:** 3D color space visualization with cluster centers
2. **Correlation Heatmap:** Color relationship matrix with hierarchical clustering
3. **Network Graph:** Spatial color relationships with edge weights
4. **Gradient Process:** Step-by-step color transition visualization
5. **Evaluation Radar:** Multi-dimensional assessment results
6. **Comparison Charts:** Before/after optimization comparisons

## Data Models

### Color Class
```python
@dataclass
class Color:
    rgb: Tuple[int, int, int]
    hsv: Tuple[float, float, float]
    lab: Tuple[float, float, float]
    hex: str
    name: Optional[str] = None
    weight: float = 1.0
    spatial_position: Optional[Tuple[float, float]] = None
    
    def distance_to(self, other: 'Color', space: str = 'lab') -> float
    def to_dict(self) -> Dict
    @classmethod
    def from_dict(cls, data: Dict) -> 'Color'
```

### ColorScheme Class
```python
@dataclass
class ColorScheme:
    id: str
    colors: List[Color]
    generation_method: str
    harmony_score: float
    historical_score: float
    comfort_score: float
    overall_score: float
    metadata: Dict
    
    def add_color(self, color: Color) -> None
    def remove_color(self, color_id: str) -> None
    def to_dict(self) -> Dict
    @classmethod
    def from_dict(cls, data: Dict) -> 'ColorScheme'
```

### SegmentationResult Class
```python
@dataclass
class SegmentationResult:
    image_id: str
    segmentation_mask: np.ndarray
    class_probabilities: Dict[str, float]
    building_mask: np.ndarray
    vegetation_mask: np.ndarray
    color_blocks: np.ndarray
    processing_time: float
    
    def get_class_coverage(self, class_name: str) -> float
    def extract_region(self, class_name: str) -> np.ndarray
    def to_dict(self) -> Dict
```

## Error Handling

### Exception Hierarchy
```python
class ColorOptimizationError(Exception):
    """Base exception for color optimization system"""
    pass

class ImageProcessingError(ColorOptimizationError):
    """Raised when image processing fails"""
    pass

class SegmentationError(ColorOptimizationError):
    """Raised when semantic segmentation fails"""
    pass

class ColorExtractionError(ColorOptimizationError):
    """Raised when color extraction fails"""
    pass

class OptimizationError(ColorOptimizationError):
    """Raised when color scheme optimization fails"""
    pass

class VisualizationError(ColorOptimizationError):
    """Raised when visualization generation fails"""
    pass
```

### Error Handling Strategy
1. **Graceful Degradation:** Continue processing remaining images when individual images fail
2. **Detailed Logging:** Capture full error context including stack traces and input parameters
3. **Recovery Mechanisms:** Implement fallback methods for critical operations
4. **User Feedback:** Provide clear error messages with suggested solutions
5. **Partial Results:** Save intermediate results to prevent complete data loss

## Testing Strategy

### Unit Testing
- Individual component testing with mock dependencies
- Color space conversion accuracy testing
- Algorithm correctness validation
- Configuration validation testing

### Integration Testing
- End-to-end pipeline testing with sample data
- Component interaction validation
- Performance benchmarking
- Memory usage monitoring

### Visual Testing
- Segmentation accuracy validation using ground truth data
- Color extraction consistency testing
- Visualization output validation
- Cross-platform compatibility testing

## Performance Considerations

### Memory Management
- Lazy loading of large image datasets
- Efficient numpy array operations
- Memory-mapped file access for large datasets
- Garbage collection optimization

### Processing Optimization
- Batch processing for similar operations
- Parallel processing using multiprocessing
- GPU acceleration for segmentation tasks
- Caching of intermediate results

### Scalability Features
- Configurable batch sizes based on available memory
- Progress tracking and resumable processing
- Distributed processing capability for large datasets
- Efficient file I/O with compression support

This design provides a robust, modular foundation for the restructured color optimization system while maintaining compatibility with existing components and adding the requested enhancements for semantic segmentation and scientific visualization.