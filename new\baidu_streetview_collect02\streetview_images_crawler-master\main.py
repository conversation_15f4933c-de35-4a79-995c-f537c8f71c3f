import geopandas as gpd
import pandas as pd
import os # 导入 os 模块
from schedule.schedule import SVI_crawler
from config.config import recorder
from utils.street2points import generate_streetview_points


def points_from_road(road_path, threshold=100):
    """_summary_

    Args:
        road_path (_type_): _road_path
        threshold (int, optional): _split road in threshold meters_. Defaults to 100.

    Returns:
        _type_: _description_
    """
    road = gpd.read_file(road_path).to_crs("epsg:4326")
    pnts = generate_streetview_points(road, threshold)
    return pnts


def run(pnts, recorder):
    recorder = SVI_crawler(pnts, recorder)
    # 在保存文件之前创建目录
    output_dir = r"./dir"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"目录 '{output_dir}' 已创建")
    recorder.to_excel(os.path.join(output_dir, "crawl_recorder.xlsx")) # 使用 os.path.join 构建完整路径


if __name__ == "__main__":
    # 如果是路网生成点 用这两行代码生成Pnts
    # road_path = r"./dir/shp/road.shp"
    # pnts = points_from_road(road_path, 50)
    # pnts.to_file(r"./data/pts.geojson")

    # 如果直接有点数据，那么直接如下读取点就好了
    csv_file_path = input("请输入包含点数据的CSV文件路径: ")
    try:
        # 假设CSV文件包含 'longitude' 和 'latitude' 列
        pnts = pd.read_csv(csv_file_path)
        # 如果您的CSV文件列名不同，请修改上面一行或在这里进行列名映射
        # 例如: pnts = pd.read_csv(csv_file_path).rename(columns={'your_lon_col': 'longitude', 'your_lat_col': 'latitude'})
        # 将DataFrame转换为GeoDataFrame，假设坐标系是EPSG:4326
        pnts = gpd.GeoDataFrame(
            pnts, geometry=gpd.points_from_xy(pnts.longitude, pnts.latitude), crs="epsg:4326"
        )
        print(f"已从CSV文件 '{csv_file_path}' 读取点数据")
    except FileNotFoundError:
        print(f"错误: 未找到文件 '{csv_file_path}'")
        exit()
    except Exception as e:
        print(f"读取CSV文件时发生错误: {e}")
        exit()

    run(pnts, recorder)
