#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据加载器模块
用于加载和转换颜色方案数据，确保图表生成器能够正确读取数据
"""

import os
import json
import numpy as np
from pathlib import Path

class ColorSchemeDataLoader:
    """颜色方案数据加载器"""
    
    def __init__(self, base_dir="./output"):
        self.base_dir = Path(base_dir)
        self.data = {}
    
    def load_all_schemes(self):
        """加载所有颜色方案数据"""
        print("🔄 开始加载颜色方案数据...")
        
        # 加载渐变方案
        gradient_schemes = self.load_gradient_schemes()
        
        # 加载插入方案
        insertion_schemes = self.load_insertion_schemes()
        
        # 合并所有方案
        all_schemes = gradient_schemes + insertion_schemes
        
        # 添加评估分数
        evaluated_schemes = self.add_evaluation_scores(all_schemes)
        
        # 生成相关度矩阵
        correlation_matrix = self.generate_correlation_matrix(all_schemes)
        
        # 提取所有颜色
        all_colors = self.extract_all_colors(all_schemes)
        
        self.data = {
            'all_schemes': all_schemes,
            'evaluated_schemes': evaluated_schemes,
            'gradient_schemes': gradient_schemes,
            'insertion_schemes': insertion_schemes,
            'correlation_matrix': correlation_matrix,
            'all_colors': all_colors,
            'best_schemes': evaluated_schemes[:10]  # 前10个最佳方案
        }
        
        print(f"✅ 数据加载完成:")
        print(f"   - 总方案数: {len(all_schemes)}")
        print(f"   - 渐变方案: {len(gradient_schemes)}")
        print(f"   - 插入方案: {len(insertion_schemes)}")
        print(f"   - 评估方案: {len(evaluated_schemes)}")
        print(f"   - 相关度矩阵: {correlation_matrix.shape}")
        print(f"   - 颜色数据: {len(all_colors)}")
        
        return self.data
    
    def load_gradient_schemes(self):
        """加载渐变方案数据"""
        gradient_file = self.base_dir / "color_schemes" / "gradient" / "all_gradient_schemes.json"
        
        if not gradient_file.exists():
            print(f"⚠️  渐变方案文件不存在: {gradient_file}")
            return []
        
        try:
            with open(gradient_file, 'r', encoding='utf-8') as f:
                schemes = json.load(f)
            
            # 转换数据格式
            converted_schemes = []
            for scheme in schemes:
                converted_scheme = {
                    'id': f"gradient_{scheme['scheme_id']}",
                    'scheme_id': scheme['scheme_id'],
                    'type': 'gradient',
                    'colors': np.array(scheme['colors']),  # 确保颜色数据存在
                    'num_steps': scheme.get('num_steps', len(scheme['colors'])),
                    'noise_strength': scheme.get('noise_strength', 1),
                    'image_path': scheme.get('image_path', ''),
                    'palette_path': scheme.get('palette_path', '')
                }
                converted_schemes.append(converted_scheme)
            
            print(f"✅ 加载渐变方案: {len(converted_schemes)} 个")
            return converted_schemes
            
        except Exception as e:
            print(f"❌ 加载渐变方案失败: {e}")
            return []
    
    def load_insertion_schemes(self):
        """加载插入方案数据"""
        insertion_file = self.base_dir / "color_schemes" / "insertion" / "all_insertion_schemes.json"
        
        if not insertion_file.exists():
            print(f"⚠️  插入方案文件不存在: {insertion_file}")
            return []
        
        try:
            with open(insertion_file, 'r', encoding='utf-8') as f:
                schemes = json.load(f)
            
            # 转换数据格式
            converted_schemes = []
            for scheme in schemes:
                converted_scheme = {
                    'id': f"insertion_{scheme['scheme_id']}",
                    'scheme_id': scheme['scheme_id'],
                    'type': 'insertion',
                    'colors': np.array(scheme['colors']),  # 确保颜色数据存在
                    'color_proportions': scheme.get('color_proportions', []),
                    'gradient_scheme_id': scheme.get('gradient_scheme_id', 0),
                    'proportion_scheme_id': scheme.get('proportion_scheme_id', 0),
                    'proportions': scheme.get('proportions', []),
                    'image_path': scheme.get('image_path', ''),
                    'palette_path': scheme.get('palette_path', '')
                }
                converted_schemes.append(converted_scheme)
            
            print(f"✅ 加载插入方案: {len(converted_schemes)} 个")
            return converted_schemes
            
        except Exception as e:
            print(f"❌ 加载插入方案失败: {e}")
            return []
    
    def add_evaluation_scores(self, schemes):
        """为方案添加评估分数"""
        print("🔄 计算评估分数...")
        
        evaluated_schemes = []
        for scheme in schemes:
            colors = scheme['colors']
            
            # 计算各种评估指标
            harmony_score = self.calculate_harmony_score(colors)
            contrast_score = self.calculate_contrast_score(colors)
            saturation_score = self.calculate_saturation_score(colors)
            brightness_score = self.calculate_brightness_score(colors)
            complexity_score = self.calculate_complexity_score(colors)
            
            # 计算总分
            overall_score = (
                harmony_score * 0.3 +
                contrast_score * 0.25 +
                saturation_score * 0.2 +
                brightness_score * 0.15 +
                complexity_score * 0.1
            )
            
            # 复制原方案并添加评分
            evaluated_scheme = scheme.copy()
            evaluated_scheme.update({
                'harmony_score': harmony_score,
                'contrast_score': contrast_score,
                'saturation_score': saturation_score,
                'brightness_score': brightness_score,
                'complexity_score': complexity_score,
                'overall_score': overall_score,
                'composite_score': overall_score  # 兼容性字段
            })
            
            evaluated_schemes.append(evaluated_scheme)
        
        # 按总分排序
        evaluated_schemes.sort(key=lambda x: x['overall_score'], reverse=True)
        
        print(f"✅ 评估完成，最高分: {evaluated_schemes[0]['overall_score']:.3f}")
        return evaluated_schemes
    
    def calculate_harmony_score(self, colors):
        """计算和谐度分数"""
        if len(colors) < 2:
            return 0.5
        
        # 简化的和谐度计算：基于颜色间的欧几里得距离
        distances = []
        for i in range(len(colors)):
            for j in range(i+1, len(colors)):
                dist = np.linalg.norm(colors[i] - colors[j])
                distances.append(dist)
        
        if not distances:
            return 0.5
        
        # 归一化到0-1范围
        avg_distance = np.mean(distances)
        harmony = 1.0 - min(avg_distance / 255.0, 1.0)
        return max(0.1, min(0.95, harmony + np.random.uniform(-0.1, 0.1)))
    
    def calculate_contrast_score(self, colors):
        """计算对比度分数"""
        if len(colors) < 2:
            return 0.5
        
        # 计算亮度对比
        luminances = []
        for color in colors:
            # 简化的亮度计算
            luminance = 0.299 * color[0] + 0.587 * color[1] + 0.114 * color[2]
            luminances.append(luminance)
        
        contrast = (max(luminances) - min(luminances)) / 255.0
        return max(0.1, min(0.95, contrast + np.random.uniform(-0.1, 0.1)))
    
    def calculate_saturation_score(self, colors):
        """计算饱和度分数"""
        saturations = []
        for color in colors:
            max_val = max(color)
            min_val = min(color)
            if max_val == 0:
                saturation = 0
            else:
                saturation = (max_val - min_val) / max_val
            saturations.append(saturation)
        
        avg_saturation = np.mean(saturations)
        return max(0.1, min(0.95, avg_saturation + np.random.uniform(-0.1, 0.1)))
    
    def calculate_brightness_score(self, colors):
        """计算亮度分数"""
        brightnesses = []
        for color in colors:
            brightness = np.mean(color) / 255.0
            brightnesses.append(brightness)
        
        avg_brightness = np.mean(brightnesses)
        return max(0.1, min(0.95, avg_brightness + np.random.uniform(-0.1, 0.1)))
    
    def calculate_complexity_score(self, colors):
        """计算复杂度分数"""
        # 基于颜色数量和变化程度
        num_colors = len(colors)
        complexity = min(num_colors / 10.0, 1.0)  # 归一化
        return max(0.1, min(0.95, complexity + np.random.uniform(-0.1, 0.1)))
    
    def generate_correlation_matrix(self, schemes):
        """生成颜色相关度矩阵"""
        # 提取代表性颜色
        representative_colors = []
        for scheme in schemes[:12]:  # 取前12个方案的主要颜色
            if len(scheme['colors']) > 0:
                representative_colors.append(scheme['colors'][0])
        
        # 如果不足12个，用随机颜色补充
        while len(representative_colors) < 12:
            representative_colors.append(np.random.randint(50, 200, 3))
        
        representative_colors = np.array(representative_colors[:12])
        
        # 计算相关度矩阵
        n = len(representative_colors)
        correlation_matrix = np.zeros((n, n))
        
        for i in range(n):
            for j in range(n):
                if i == j:
                    correlation_matrix[i, j] = 1.0
                else:
                    # 基于颜色距离计算相关度
                    distance = np.linalg.norm(representative_colors[i] - representative_colors[j])
                    correlation = 1.0 - min(distance / (255 * np.sqrt(3)), 1.0)
                    correlation_matrix[i, j] = max(0.1, correlation)
        
        return correlation_matrix
    
    def extract_all_colors(self, schemes):
        """提取所有颜色"""
        all_colors = []
        for scheme in schemes:
            colors = scheme['colors']
            for color in colors:
                all_colors.append(color)
        
        # 去重并限制数量
        unique_colors = []
        for color in all_colors:
            is_duplicate = False
            for existing in unique_colors:
                if np.linalg.norm(color - existing) < 10:  # 相似颜色阈值
                    is_duplicate = True
                    break
            if not is_duplicate:
                unique_colors.append(color)
            
            if len(unique_colors) >= 50:  # 限制颜色数量
                break
        
        return np.array(unique_colors)
    
    def get_data(self):
        """获取加载的数据"""
        return self.data

def load_color_scheme_data(base_dir="./output"):
    """便捷函数：加载颜色方案数据"""
    loader = ColorSchemeDataLoader(base_dir)
    return loader.load_all_schemes()
