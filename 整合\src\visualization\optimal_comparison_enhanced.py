#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强版最优方案对比分析图生成器
解决布局问题，提升视觉效果
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Rectangle, FancyBboxPatch, Circle
import seaborn as sns
from scipy import stats
import matplotlib.gridspec as gridspec
from mpl_toolkits.mplot3d import Axes3D
import logging

class OptimalComparisonEnhanced:
    """增强版最优方案对比分析图生成器"""
    
    def __init__(self, config=None):
        self.logger = logging.getLogger(__name__)
        
        # 默认现代化配色方案
        self.default_colors = {
            'primary': '#1f77b4',      # 现代蓝
            'secondary': '#ff7f0e',    # 活力橙
            'success': '#2ca02c',      # 成功绿
            'warning': '#d62728',      # 警告红
            'info': '#9467bd',         # 信息紫
            'accent1': '#8c564b',      # 棕色
            'accent2': '#e377c2',      # 粉色
            'accent3': '#7f7f7f',      # 灰色
            'gradient_start': '#667eea', # 渐变起始
            'gradient_end': '#764ba2'    # 渐变结束
        }
        
        # 当前使用的配色方案（将根据最佳方案颜色动态调整）
        self.colors = self.default_colors.copy()
        self.color_sequence = list(self.default_colors.values())[:8]
    
    def _generate_color_scheme_from_best_colors(self, best_scheme_colors):
        """基于最佳方案的颜色生成配色方案"""
        try:
            if not best_scheme_colors or len(best_scheme_colors) == 0:
                return self.default_colors.copy()
            
            # 确保颜色格式正确
            colors = np.array(best_scheme_colors)
            if colors.max() > 1:
                colors = colors / 255.0
            
            # 选择主要颜色（前6个）
            main_colors = colors[:6] if colors.shape[0] >= 6 else colors
            
            # 生成基于主要颜色的配色方案
            generated_colors = {}
            color_names = ['primary', 'secondary', 'success', 'warning', 'info', 'accent1']
            
            for i, color_name in enumerate(color_names):
                if i < main_colors.shape[0]:
                    # 转换为十六进制
                    hex_color = '#{:02x}{:02x}{:02x}'.format(
                        int(main_colors[i][0] * 255),
                        int(main_colors[i][1] * 255),
                        int(main_colors[i][2] * 255)
                    )
                    generated_colors[color_name] = hex_color
                else:
                    generated_colors[color_name] = self.default_colors[color_name]
            
            # 添加其他颜色
            generated_colors.update({
                'accent2': self.default_colors['accent2'],
                'accent3': self.default_colors['accent3'],
                'gradient_start': generated_colors['primary'],
                'gradient_end': generated_colors['secondary']
            })
            
            return generated_colors
            
        except Exception as e:
            self.logger.warning(f"生成配色方案失败，使用默认方案: {str(e)}")
            return self.default_colors.copy()
    
    def generate_enhanced_optimal_comparison(self, schemes_data, output_path):
        """生成增强版最优方案对比分析图"""
        try:
            self.logger.info("开始生成增强版最优方案对比分析图...")
            
            # 基于最佳方案的颜色生成配色方案
            if schemes_data and len(schemes_data) > 0:
                best_colors = schemes_data[0].get('colors', None)
                if best_colors is not None:
                    self.colors = self._generate_color_scheme_from_best_colors(best_colors)
                    self.color_sequence = [
                        self.colors['primary'], self.colors['secondary'], self.colors['success'],
                        self.colors['warning'], self.colors['info'], self.colors['accent1'],
                        self.colors['accent2'], self.colors['accent3']
                    ]
                    self.logger.info("已基于最佳方案颜色生成配色方案")
            
            # 创建大尺寸高分辨率图表
            fig = plt.figure(figsize=(28, 20), dpi=300)
            
            # 使用更合理的网格布局，增加间距避免重叠
            gs = gridspec.GridSpec(3, 4, 
                                 height_ratios=[1.3, 1.1, 0.9], 
                                 width_ratios=[1.2, 1.2, 1, 0.8],
                                 hspace=0.45, wspace=0.35)
            
            # 子图A: 3D立体综合评分对比
            ax1 = fig.add_subplot(gs[0, :2])
            self._plot_3d_score_comparison(ax1, schemes_data)
            self._add_subplot_label(ax1, 'A', x=-0.08, y=1.02)
            
            # 子图B: 现代化雷达图
            ax2 = fig.add_subplot(gs[0, 2])
            self._plot_modern_radar_chart(ax2, schemes_data[:3])
            self._add_subplot_label(ax2, 'B', x=-0.15, y=1.02)
            
            # 子图C: 交互式颜色方案展示
            ax3 = fig.add_subplot(gs[0, 3])
            self._plot_color_scheme_showcase(ax3, schemes_data[:4])
            self._add_subplot_label(ax3, 'C', x=-0.2, y=1.02)
            
            # 子图D: 动态统计分析
            ax4 = fig.add_subplot(gs[1, :2])
            self._plot_dynamic_statistics(ax4, schemes_data)
            self._add_subplot_label(ax4, 'D', x=-0.08, y=1.02)
            
            # 子图E: 稳定性热力图
            ax5 = fig.add_subplot(gs[1, 2:])
            self._plot_stability_heatmap(ax5, schemes_data[:6])
            self._add_subplot_label(ax5, 'E', x=-0.08, y=1.02)
            
            # 子图F: 综合推荐仪表盘
            ax6 = fig.add_subplot(gs[2, :])
            self._plot_recommendation_dashboard(ax6, schemes_data[:5])
            self._add_subplot_label(ax6, 'F', x=-0.05, y=1.02)
            
            # 调整布局并保存，增加更多边距
            plt.tight_layout(pad=4.0, h_pad=6.0, w_pad=5.0)
            fig.savefig(output_path, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none', pad_inches=0.3)
            plt.close(fig)
            
            self.logger.info(f"增强版最优方案对比分析图生成成功: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"生成增强版图表失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None    

    def _add_subplot_label(self, ax, label, x=-0.1, y=1.05):
        """添加子图标签"""
        ax.text(x, y, f'({label})', transform=ax.transAxes,
               fontsize=14, fontweight='bold', va='bottom', ha='right')
    
    def _plot_3d_score_comparison(self, ax, schemes_data):
        """绘制3D立体综合评分对比"""
        n_schemes = min(8, len(schemes_data))
        schemes = schemes_data[:n_schemes]
        
        # 提取评分数据
        scores = []
        harmony_scores = []
        contrast_scores = []
        
        for scheme in schemes:
            overall = scheme.get('overall_score', 0.7)
            harmony = scheme.get('harmony_score', 0.7)
            contrast = scheme.get('contrast_score', 0.6)
            
            scores.append(overall)
            harmony_scores.append(harmony)
            contrast_scores.append(contrast)
        
        # 创建3D效果的柱状图
        x_pos = np.arange(n_schemes)
        
        # 主柱状图
        bars = ax.bar(x_pos, scores, color=self.color_sequence[:n_schemes], 
                     alpha=0.8, edgecolor='black', linewidth=1.5)
        
        # 添加渐变效果
        for i, bar in enumerate(bars):
            # 创建渐变效果
            height = bar.get_height()
            width = bar.get_width()
            x = bar.get_x()
            
            # 添加高光效果
            highlight = Rectangle((x + width*0.1, height*0.8), width*0.8, height*0.15,
                                facecolor='white', alpha=0.3)
            ax.add_patch(highlight)
            
            # 添加阴影效果
            shadow = Rectangle((x + 0.02, -0.01), width, height,
                             facecolor='gray', alpha=0.2, zorder=0)
            ax.add_patch(shadow)
        
        # 添加数值标签
        for i, (bar, score) in enumerate(zip(bars, scores)):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                   f'{score:.3f}', ha='center', va='bottom',
                   fontweight='bold', fontsize=12)
        
        # 添加统计线
        mean_score = np.mean(scores)
        ax.axhline(mean_score, color='red', linestyle='--', linewidth=2, 
                  alpha=0.7, label=f'Average: {mean_score:.3f}')
        
        # 标记最优方案
        best_idx = np.argmax(scores)
        ax.annotate(f'Best\n{scores[best_idx]:.3f}', 
                   xy=(best_idx, scores[best_idx]),
                   xytext=(best_idx, scores[best_idx] + 0.2),
                   arrowprops=dict(arrowstyle='->', color='red', lw=2),
                   ha='center', fontweight='bold', color='red', fontsize=12)
        
        ax.set_title('3D Enhanced Score Comparison', fontweight='bold', fontsize=16, pad=20)
        ax.set_xlabel('Schemes', fontweight='bold', fontsize=12)
        ax.set_ylabel('Overall Score', fontweight='bold', fontsize=12)
        ax.set_xticks(x_pos)
        ax.set_xticklabels([f'Scheme {i+1}' for i in range(n_schemes)])
        ax.legend(fontsize=10)
        ax.grid(True, alpha=0.3)
        ax.set_ylim(0, max(scores) * 1.3)
    
    def _plot_modern_radar_chart(self, ax, schemes_data):
        """绘制现代化雷达图"""
        metrics = ['Harmony', 'Contrast', 'Saturation', 'Brightness', 'Complexity']
        n_metrics = len(metrics)
        
        # 计算角度
        angles = np.linspace(0, 2 * np.pi, n_metrics, endpoint=False).tolist()
        angles += angles[:1]  # 闭合图形
        
        # 为每个方案绘制雷达图
        for i, scheme in enumerate(schemes_data):
            values = [
                scheme.get('harmony_score', 0.7),
                scheme.get('contrast_score', 0.6),
                scheme.get('saturation_score', 0.65),
                scheme.get('brightness_score', 0.6),
                scheme.get('complexity_score', 0.5)
            ]
            values += values[:1]  # 闭合图形
            
            color = self.color_sequence[i]
            
            # 绘制雷达图
            ax.plot(angles, values, 'o-', linewidth=2, label=f'Scheme {i+1}',
                   color=color, markersize=6)
            ax.fill(angles, values, alpha=0.25, color=color)
        
        # 设置标签
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metrics, fontsize=10)
        ax.set_ylim(0, 1)
        ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
        ax.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'], fontsize=8)
        ax.grid(True, alpha=0.3)
        
        ax.set_title('Multi-Dimensional Performance Radar', fontweight='bold', fontsize=12, pad=15)
        ax.legend(loc='upper right', bbox_to_anchor=(1.2, 1.0), fontsize=9)
    
    def _plot_color_scheme_showcase(self, ax, schemes_data):
        """绘制交互式颜色方案展示"""
        n_schemes = len(schemes_data)
        
        for i, scheme in enumerate(schemes_data):
            colors = scheme.get('colors', np.random.randint(0, 256, (5, 3)))
            
            # 确保颜色格式正确
            if isinstance(colors, np.ndarray) and colors.max() > 1:
                colors = colors / 255.0
            
            # 计算位置
            y_pos = 0.8 - i * 0.2
            
            # 绘制颜色条
            for j, color in enumerate(colors[:5]):  # 最多显示5个颜色
                x_pos = j * 0.18
                
                # 主颜色块
                circle = Circle((x_pos + 0.1, y_pos), 0.06, 
                              facecolor=color, edgecolor='black', linewidth=1.5)
                ax.add_patch(circle)
                
                # 添加RGB标签
                if hasattr(color, '__len__') and len(color) >= 3:
                    rgb_text = f'RGB\n({int(color[0]*255)},{int(color[1]*255)},{int(color[2]*255)})'
                    ax.text(x_pos + 0.1, y_pos - 0.12, rgb_text, 
                           ha='center', va='top', fontsize=6)
            
            # 添加方案标签
            ax.text(-0.05, y_pos, f'Scheme {i+1}', ha='right', va='center', 
                   fontweight='bold', fontsize=10)
        
        ax.set_xlim(-0.1, 1.0)
        ax.set_ylim(0, 1)
        ax.set_title('Color Schemes Showcase', fontweight='bold', fontsize=12, pad=15)
        ax.axis('off')
    
    def _plot_dynamic_statistics(self, ax, schemes_data):
        """绘制动态统计分析"""
        # 提取所有评分
        all_scores = []
        harmony_scores = []
        contrast_scores = []
        
        for scheme in schemes_data:
            all_scores.append(scheme.get('overall_score', 0.7))
            harmony_scores.append(scheme.get('harmony_score', 0.7))
            contrast_scores.append(scheme.get('contrast_score', 0.6))
        
        # 创建箱线图和小提琴图组合
        data_to_plot = [all_scores, harmony_scores, contrast_scores]
        labels = ['Overall', 'Harmony', 'Contrast']
        
        # 小提琴图
        parts = ax.violinplot(data_to_plot, positions=[1, 2, 3], showmeans=True, showmedians=True)
        
        # 设置颜色
        colors = [self.colors['primary'], self.colors['success'], self.colors['warning']]
        for pc, color in zip(parts['bodies'], colors):
            pc.set_facecolor(color)
            pc.set_alpha(0.7)
        
        # 添加统计信息
        for i, (data, label) in enumerate(zip(data_to_plot, labels)):
            mean_val = np.mean(data)
            std_val = np.std(data)
            ax.text(i + 1, max(data) + 0.05, f'μ={mean_val:.3f}\nσ={std_val:.3f}',
                   ha='center', va='bottom', fontsize=10, fontweight='bold')
        
        ax.set_xticks([1, 2, 3])
        ax.set_xticklabels(labels)
        ax.set_title('Dynamic Statistical Analysis', fontweight='bold', fontsize=14)
        ax.set_ylabel('Score Distribution', fontweight='bold')
        ax.grid(True, alpha=0.3)
    
    def _plot_stability_heatmap(self, ax, schemes_data):
        """绘制稳定性热力图"""
        metrics = ['Harmony', 'Contrast', 'Saturation', 'Brightness', 'Complexity']
        n_schemes = len(schemes_data)
        
        # 创建数据矩阵
        data_matrix = []
        for scheme in schemes_data:
            row = [
                scheme.get('harmony_score', 0.7),
                scheme.get('contrast_score', 0.6),
                scheme.get('saturation_score', 0.65),
                scheme.get('brightness_score', 0.6),
                scheme.get('complexity_score', 0.5)
            ]
            data_matrix.append(row)
        
        data_matrix = np.array(data_matrix)
        
        # 创建热力图
        im = ax.imshow(data_matrix, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)
        
        # 设置标签
        ax.set_xticks(range(len(metrics)))
        ax.set_yticks(range(n_schemes))
        ax.set_xticklabels(metrics, rotation=45, ha='right')
        ax.set_yticklabels([f'Scheme {i+1}' for i in range(n_schemes)])
        
        # 添加数值标注
        for i in range(n_schemes):
            for j in range(len(metrics)):
                text_color = "white" if data_matrix[i, j] > 0.5 else "black"
                ax.text(j, i, f'{data_matrix[i, j]:.2f}',
                       ha="center", va="center", color=text_color, fontweight='bold')
        
        ax.set_title('Stability Heatmap Analysis', fontweight='bold', fontsize=14, pad=15)
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Performance Score', rotation=270, labelpad=15)
    
    def _plot_recommendation_dashboard(self, ax, schemes_data):
        """绘制综合推荐仪表盘"""
        n_schemes = len(schemes_data)
        
        # 计算推荐度
        recommendations = []
        for scheme in schemes_data:
            harmony = scheme.get('harmony_score', 0.7)
            overall = scheme.get('overall_score', 0.7)
            contrast = scheme.get('contrast_score', 0.6)
            
            # 综合推荐度计算
            recommendation = (harmony * 0.4 + overall * 0.4 + contrast * 0.2)
            recommendations.append(recommendation)
        
        # 创建仪表盘样式的条形图
        x_pos = np.arange(n_schemes)
        bars = ax.barh(x_pos, recommendations, color=self.color_sequence[:n_schemes], 
                      alpha=0.8, edgecolor='black', linewidth=1.5)
        
        # 添加推荐等级标识
        for i, (bar, rec) in enumerate(zip(bars, recommendations)):
            width = bar.get_width()
            
            # 推荐等级
            if rec >= 0.8:
                grade = "A+"
                grade_color = "green"
            elif rec >= 0.7:
                grade = "A"
                grade_color = "lightgreen"
            elif rec >= 0.6:
                grade = "B"
                grade_color = "orange"
            else:
                grade = "C"
                grade_color = "red"
            
            # 添加等级标签
            ax.text(width + 0.02, i, f'{rec:.3f} ({grade})', 
                   va='center', fontweight='bold', color=grade_color, fontsize=11)
        
        # 添加推荐阈值线
        ax.axvline(0.8, color='green', linestyle='--', alpha=0.7, label='Excellent (0.8+)')
        ax.axvline(0.7, color='orange', linestyle='--', alpha=0.7, label='Good (0.7+)')
        ax.axvline(0.6, color='red', linestyle='--', alpha=0.7, label='Fair (0.6+)')
        
        ax.set_yticks(x_pos)
        ax.set_yticklabels([f'Scheme {i+1}' for i in range(n_schemes)])
        ax.set_xlabel('Recommendation Score', fontweight='bold')
        ax.set_title('Comprehensive Recommendation Dashboard', fontweight='bold', fontsize=16, pad=20)
        ax.legend(loc='lower right', fontsize=10)
        ax.grid(True, alpha=0.3, axis='x')
        ax.set_xlim(0, 1.0)

def main():
    """测试增强版最优方案对比分析图"""
    # 创建测试数据
    test_schemes = []
    for i in range(10):
        scheme = {
            'id': f'scheme_{i+1}',
            'colors': np.random.rand(6, 3),
            'harmony_score': np.random.uniform(0.5, 0.95),
            'contrast_score': np.random.uniform(0.4, 0.9),
            'saturation_score': np.random.uniform(0.4, 0.85),
            'brightness_score': np.random.uniform(0.35, 0.8),
            'complexity_score': np.random.uniform(0.3, 0.8),
            'overall_score': np.random.uniform(0.5, 0.9)
        }
        test_schemes.append(scheme)
    
    # 按评分排序
    test_schemes.sort(key=lambda x: x['overall_score'], reverse=True)
    
    # 生成图表
    generator = OptimalComparisonEnhanced()
    output_path = "07_optimal_comparison_enhanced.png"
    
    result = generator.generate_enhanced_optimal_comparison(test_schemes, output_path)
    
    if result:
        print(f"✅ 增强版最优方案对比分析图生成成功: {result}")
    else:
        print("❌ 图表生成失败")

if __name__ == "__main__":
    main()