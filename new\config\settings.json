﻿{
  "system": {
    "name": "建筑立面色彩优化系统",
    "version": "3.0.0",
    "description": "基于语义分割和机器学习的历史街区建筑色彩优化系统"
  },
  "data": {
    "input_dir": "data/input/street_view",
    "reference_colors_dir": "data/input/reference_colors",
    "output_dir": "data/output",
    "supported_formats": ["jpg", "jpeg", "png", "bmp"],
    "max_image_size": 4096,
    "batch_size": 10
  },
  "preprocessing": {
    "enable_shadow_removal": true,
    "enable_illumination_normalization": true,
    "enable_color_enhancement": true,
    "enable_noise_reduction": true,
    "resize_threshold": 1000,
    "target_size": 800,
    "quality_threshold": 0.7
  },
  "segmentation": {
    "model_name": "psp_resnet101_ade",
    "use_gpu": true,
    "batch_size": 4,
    "output_formats": {
      "color_blocks": true,
      "buildings_transparent": true,
      "buildings_white": true,
      "vegetation_transparent": true,
      "building_contour": true
    },
    "building_classes": [1, 2, 25, 27],
    "vegetation_classes": [4, 9, 17, 18, 19]
  },
  "clustering": {
    "n_colors_primary": 12,
    "n_colors_secondary": 12,
    "color_spaces": ["rgb", "hsv", "lab"],
    "clustering_method": "kmeans",
    "max_iterations": 300,
    "tolerance": 0.001,
    "init_method": "k-means++"
  },
  "correlation": {
    "correlation_threshold": 0.7,
    "top_colors_count": 3,
    "spatial_weight_decay": 0.1,
    "adjacency_method": "delaunay"
  },
  "color_generation": {
    "gradient_steps": 50,
    "noise_factor": 0.1,
    "bezier_control_points": 4,
    "interpolation_method": "cubic",
    "primary_secondary_ratio": [0.7, 0.3]
  },
  "evaluation": {
    "weights": {
      "harmony": 0.3,
      "historical_coordination": 0.3,
      "visual_comfort": 0.2,
      "multi_scale_consistency": 0.2
    },
    "harmony_types": ["complementary", "analogous", "triadic", "split_complementary"],
    "comfort_metrics": ["contrast_ratio", "saturation_balance", "brightness_distribution"]
  },
  "visualization": {
    "language": "english",
    "style": "scientific",
    "dpi": 300,
    "figure_size": [12, 8],
    "color_palette": "viridis",
    "font_family": "Arial",
    "font_size": 12,
    "export_formats": ["png", "svg", "pdf"]
  },
  "logging": {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file_handler": true,
    "console_handler": true,
    "max_file_size": "10MB",
    "backup_count": 5
  },
  "performance": {
    "enable_multiprocessing": true,
    "max_workers": 4,
    "memory_limit": "8GB",
    "enable_caching": true,
    "cache_size": "1GB"
  }
}
