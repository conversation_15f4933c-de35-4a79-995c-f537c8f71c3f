#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
建筑立面色彩优化系统
版本: 2.0.0 (重构版)
作者: 建筑色彩优化团队

主要模块：
- data_processing: 数据处理模块（图像预处理、聚类分析、相关度计算）
- color_generation: 色彩生成模块（渐变生成、插入生成）
- evaluation: 评估系统（多维度评估、最佳方案选择）
- visualization: 可视化系统（SCI标准图表、中文支持）
"""

__version__ = "2.0.0"
__author__ = "建筑色彩优化团队"
__email__ = "<EMAIL>"
__description__ = "基于机器学习的建筑立面色彩优化系统"

# 核心模块导入
try:
    # 数据处理模块
    from .data_processing import (
        PrimaryClusterer,
        SecondaryClusterer,
        ColorCorrelation,
        ImagePreprocessor
    )

    # 色彩生成模块
    from .color_generation import (
        GradientGenerator,
        InsertionGenerator
    )

    # 评估系统
    from .evaluation import ColorEvaluator

    # 可视化系统
    from .visualization import (
        EnhancedSCICharts,
        SCIChartGenerator,
        SCIStyle
    )

    # 工具函数
    from .utils import (
        ensure_dir,
        get_colors_from_image,
        rgb_to_lab,
        lab_to_rgb,
        create_color_palette,
        create_color_block
    )

    # 数据管理
    from .data_loader import DataLoader
    from .data_validator import DataValidator
    from .data_export import DataExporter

    # 定义公开接口
    __all__ = [
        # 数据处理
        'PrimaryClusterer', 'SecondaryClusterer', 'ColorCorrelation', 'ImagePreprocessor',
        # 色彩生成
        'GradientGenerator', 'InsertionGenerator',
        # 评估系统
        'ColorEvaluator',
        # 可视化
        'EnhancedSCICharts', 'SCIChartGenerator', 'SCIStyle',
        # 工具函数
        'ensure_dir', 'get_colors_from_image', 'rgb_to_lab', 'lab_to_rgb',
        'create_color_palette', 'create_color_block',
        # 数据管理
        'DataLoader', 'DataValidator', 'DataExporter'
    ]

    # 模块状态
    _import_status = "SUCCESS"
    _missing_modules = []

except ImportError as e:
    # 记录导入失败的模块
    _import_status = "PARTIAL"
    _missing_modules = [str(e)]

    # 定义最小可用接口
    __all__ = []

    print(f"⚠️ 模块导入警告: {e}")
    print("🔧 系统将以降级模式运行，部分功能可能不可用")

def get_module_info():
    """获取模块信息"""
    return {
        'version': __version__,
        'author': __author__,
        'description': __description__,
        'import_status': _import_status,
        'missing_modules': _missing_modules,
        'available_modules': __all__
    }

def check_dependencies():
    """检查依赖项"""
    import importlib

    required_packages = [
        'numpy', 'scikit-learn', 'PIL', 'pandas',
        'matplotlib', 'networkx', 'cv2'
    ]

    missing_packages = []
    for package in required_packages:
        try:
            importlib.import_module(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("📦 请运行: pip install -r requirements.txt")
        return False
    else:
        print("✅ 所有依赖包已安装")
        return True