﻿#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志系统模块
提供结构化的日志记录功能，支持文件和控制台输出

作者: 建筑色彩优化团队
版本: 3.0.0
"""

import logging
import logging.handlers
import os
import sys
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional


class ColorOptimizationLogger:
    """
    色彩优化系统专用日志记录器
    
    功能：
    1. 结构化日志记录（JSON格式）
    2. 多处理器支持（文件+控制台）
    3. 日志轮转和大小限制
    4. 性能指标跟踪
    5. 错误上下文保存
    """
    
    def __init__(self, name: str, config: Dict[str, Any]):
        """
        初始化日志记录器
        
        参数:
            name (str): 日志记录器名称
            config (Dict[str, Any]): 日志配置参数
        """
        self.name = name
        self.config = config
        self.logger = logging.getLogger(name)
        
        # 设置日志级别
        level = getattr(logging, config.get('level', 'INFO').upper())
        self.logger.setLevel(level)
        
        # 清除现有处理器（避免重复）
        self.logger.handlers.clear()
        
        # 创建日志目录
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        # 设置处理器
        self._setup_handlers()
        
        # 记录系统启动信息
        self.info("日志系统初始化完成", {
            'logger_name': name,
            'log_level': config.get('level', 'INFO'),
            'log_dir': str(self.log_dir)
        })
    
    def _setup_handlers(self) -> None:
        """
        设置日志处理器（文件处理器和控制台处理器）
        """
        # 创建格式化器
        formatter = self._create_formatter()
        
        # 文件处理器（如果启用）
        if self.config.get('file_handler', True):
            self._setup_file_handler(formatter)
        
        # 控制台处理器（如果启用）
        if self.config.get('console_handler', True):
            self._setup_console_handler(formatter)
    
    def _create_formatter(self) -> logging.Formatter:
        """
        创建日志格式化器
        
        返回:
            logging.Formatter: 格式化器对象
        """
        # 获取格式字符串
        format_string = self.config.get(
            'format',
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 创建格式化器
        formatter = logging.Formatter(
            format_string,
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        return formatter
    
    def _setup_file_handler(self, formatter: logging.Formatter) -> None:
        """
        设置文件处理器，支持日志轮转
        
        参数:
            formatter (logging.Formatter): 日志格式化器
        """
        # 生成日志文件名
        timestamp = datetime.now().strftime('%Y%m%d')
        log_file = self.log_dir / f"{self.name}_{timestamp}.log"
        
        # 解析文件大小限制
        max_bytes = self._parse_size(self.config.get('max_file_size', '10MB'))
        backup_count = self.config.get('backup_count', 5)
        
        # 创建轮转文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
    
    def _setup_console_handler(self, formatter: logging.Formatter) -> None:
        """
        设置控制台处理器
        
        参数:
            formatter (logging.Formatter): 日志格式化器
        """
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
    
    def _parse_size(self, size_str: str) -> int:
        """
        解析大小字符串（如 '10MB'）为字节数
        
        参数:
            size_str (str): 大小字符串
        
        返回:
            int: 字节数
        """
        size_str = size_str.upper()
        
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def _format_extra_data(self, extra: Optional[Dict[str, Any]]) -> str:
        """
        格式化额外数据为JSON字符串
        
        参数:
            extra (Dict[str, Any], optional): 额外数据
        
        返回:
            str: JSON格式的字符串
        """
        if not extra:
            return ""
        
        try:
            return f" | Extra: {json.dumps(extra, ensure_ascii=False, default=str)}"
        except Exception:
            return f" | Extra: {str(extra)}"
    
    def info(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
        """
        记录信息级别日志
        
        参数:
            message (str): 日志消息
            extra (Dict[str, Any], optional): 额外的上下文数据
        """
        full_message = message + self._format_extra_data(extra)
        self.logger.info(full_message)
    
    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
        """
        记录调试级别日志
        
        参数:
            message (str): 日志消息
            extra (Dict[str, Any], optional): 额外的上下文数据
        """
        full_message = message + self._format_extra_data(extra)
        self.logger.debug(full_message)
    
    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
        """
        记录警告级别日志
        
        参数:
            message (str): 日志消息
            extra (Dict[str, Any], optional): 额外的上下文数据
        """
        full_message = message + self._format_extra_data(extra)
        self.logger.warning(full_message)
    
    def error(self, message: str, exception: Optional[Exception] = None, 
              extra: Optional[Dict[str, Any]] = None) -> None:
        """
        记录错误级别日志
        
        参数:
            message (str): 日志消息
            exception (Exception, optional): 异常对象
            extra (Dict[str, Any], optional): 额外的上下文数据
        """
        # 构建错误信息
        error_info = {'timestamp': datetime.now().isoformat()}
        
        if exception:
            error_info.update({
                'exception_type': type(exception).__name__,
                'exception_message': str(exception),
                'exception_args': exception.args
            })
        
        if extra:
            error_info.update(extra)
        
        full_message = message + self._format_extra_data(error_info)
        
        # 记录错误日志
        if exception:
            self.logger.error(full_message, exc_info=True)
        else:
            self.logger.error(full_message)
    
    def critical(self, message: str, exception: Optional[Exception] = None,
                 extra: Optional[Dict[str, Any]] = None) -> None:
        """
        记录严重错误级别日志
        
        参数:
            message (str): 日志消息
            exception (Exception, optional): 异常对象
            extra (Dict[str, Any], optional): 额外的上下文数据
        """
        # 构建严重错误信息
        critical_info = {
            'timestamp': datetime.now().isoformat(),
            'severity': 'CRITICAL'
        }
        
        if exception:
            critical_info.update({
                'exception_type': type(exception).__name__,
                'exception_message': str(exception),
                'exception_args': exception.args
            })
        
        if extra:
            critical_info.update(extra)
        
        full_message = message + self._format_extra_data(critical_info)
        
        # 记录严重错误日志
        if exception:
            self.logger.critical(full_message, exc_info=True)
        else:
            self.logger.critical(full_message)
    
    def log_performance(self, operation: str, duration: float, 
                       extra: Optional[Dict[str, Any]] = None) -> None:
        """
        记录性能指标
        
        参数:
            operation (str): 操作名称
            duration (float): 执行时间（秒）
            extra (Dict[str, Any], optional): 额外的性能数据
        """
        perf_data = {
            'operation': operation,
            'duration_seconds': round(duration, 4),
            'performance_log': True
        }
        
        if extra:
            perf_data.update(extra)
        
        self.info(f"性能指标 - {operation}", perf_data)
    
    def log_progress(self, current: int, total: int, operation: str = "处理中") -> None:
        """
        记录进度信息
        
        参数:
            current (int): 当前进度
            total (int): 总数
            operation (str): 操作描述
        """
        percentage = (current / total) * 100 if total > 0 else 0
        
        progress_data = {
            'current': current,
            'total': total,
            'percentage': round(percentage, 2),
            'progress_log': True
        }
        
        self.info(f"{operation} - 进度: {current}/{total} ({percentage:.1f}%)", progress_data)
    
    def set_level(self, level: str) -> None:
        """
        动态设置日志级别
        
        参数:
            level (str): 日志级别 ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
        """
        try:
            log_level = getattr(logging, level.upper())
            self.logger.setLevel(log_level)
            self.info(f"日志级别已更改为: {level.upper()}")
        except AttributeError:
            self.error(f"无效的日志级别: {level}")


class LoggerManager:
    """
    日志管理器，管理多个日志记录器实例
    """
    
    def __init__(self):
        """初始化日志管理器"""
        self.loggers: Dict[str, ColorOptimizationLogger] = {}
        self.default_config = {
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'file_handler': True,
            'console_handler': True,
            'max_file_size': '10MB',
            'backup_count': 5
        }
    
    def get_logger(self, name: str, config: Optional[Dict[str, Any]] = None) -> ColorOptimizationLogger:
        """
        获取或创建日志记录器
        
        参数:
            name (str): 日志记录器名称
            config (Dict[str, Any], optional): 日志配置，如果为None则使用默认配置
        
        返回:
            ColorOptimizationLogger: 日志记录器实例
        """
        if name not in self.loggers:
            logger_config = config or self.default_config
            self.loggers[name] = ColorOptimizationLogger(name, logger_config)
        
        return self.loggers[name]
    
    def set_global_level(self, level: str) -> None:
        """
        设置所有日志记录器的级别
        
        参数:
            level (str): 日志级别
        """
        for logger in self.loggers.values():
            logger.set_level(level)


# 全局日志管理器实例
logger_manager = LoggerManager()

def get_logger(name: str, config: Optional[Dict[str, Any]] = None) -> ColorOptimizationLogger:
    """
    获取日志记录器的便捷函数
    
    参数:
        name (str): 日志记录器名称
        config (Dict[str, Any], optional): 日志配置
    
    返回:
        ColorOptimizationLogger: 日志记录器实例
    """
    return logger_manager.get_logger(name, config)


if __name__ == "__main__":
    # 测试代码
    import time
    
    # 创建测试日志记录器
    test_config = {
        'level': 'DEBUG',
        'file_handler': True,
        'console_handler': True,
        'max_file_size': '1MB',
        'backup_count': 3
    }
    
    logger = get_logger('test_logger', test_config)
    
    # 测试各种日志级别
    logger.debug("这是调试信息", {'debug_data': 'test'})
    logger.info("这是信息日志", {'info_data': 'test'})
    logger.warning("这是警告信息", {'warning_data': 'test'})
    
    # 测试错误日志
    try:
        raise ValueError("测试异常")
    except Exception as e:
        logger.error("捕获到异常", e, {'error_context': 'test'})
    
    # 测试性能日志
    start_time = time.time()
    time.sleep(0.1)  # 模拟操作
    duration = time.time() - start_time
    logger.log_performance("测试操作", duration, {'test_param': 'value'})
    
    # 测试进度日志
    for i in range(1, 6):
        logger.log_progress(i, 5, "测试进度")
        time.sleep(0.1)
    
    print("日志测试完成，请检查 logs/ 目录中的日志文件")
